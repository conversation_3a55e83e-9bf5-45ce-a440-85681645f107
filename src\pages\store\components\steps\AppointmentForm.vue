<template>
  <view class="appointment-form">
    <view class="form-section">
      <view class="section-title">
        <view class="title-icon">
          <text class="icon">📅</text>
        </view>
        <text class="title-text">预约安装服务</text>
      </view>

      <view class="form-grid">
        <view class="form-row">
          <view class="form-field" :class="{ 'has-error': errors.contactName }">
            <view class="field-label">
              <text class="required">*</text>
              <text>联系人姓名</text>
            </view>
            <input
              class="field-input"
              v-model.trim="form.contactName"
              placeholder="请输入联系人姓名"
              @input="validateField('contactName')"
              @blur="validateField('contactName')"
            />
            <text v-if="errors.contactName" class="error-message">{{ errors.contactName }}</text>
          </view>
        </view>

        <view class="form-row">
          <view class="form-field" :class="{ 'has-error': errors.contactPhone }">
            <view class="field-label">
              <text class="required">*</text>
              <text>联系电话</text>
            </view>
            <input
              class="field-input"
              v-model.trim="form.contactPhone"
              placeholder="请输入联系电话"
              type="number"
              maxlength="11"
              @input="validateField('contactPhone')"
              @blur="validateField('contactPhone')"
            />
            <text v-if="errors.contactPhone" class="error-message">{{ errors.contactPhone }}</text>
          </view>
        </view>

        <view class="form-row">
          <view class="form-field" :class="{ 'has-error': errors.date }">
            <view class="field-label">
              <text class="required">*</text>
              <text>预约日期</text>
            </view>
            <picker
              class="field-picker"
              mode="date"
              :value="form.date"
              :start="startDate"
              :end="endDate"
              @change="onDateChange"
            >
              <view class="picker-content">
                <text :class="{ 'placeholder': !form.date }">
                  {{ form.date || '请选择预约日期' }}
                </text>
                <!-- <text class="picker-arrow">></text> -->
              </view>
            </picker>
            <text v-if="errors.date" class="error-message">{{ errors.date }}</text>
          </view>
        </view>

        <view class="form-row">
          <view class="form-field" :class="{ 'has-error': errors.timeSlot }">
            <view class="field-label">
              <text class="required">*</text>
              <text>预约时间段</text>
            </view>
            <picker
              class="field-picker"
              :value="timeSlotIndex"
              :range="timeSlots"
              @change="onTimeSlotChange"
            >
              <view class="picker-content">
                <text :class="{ 'placeholder': !form.timeSlot }">
                  {{ form.timeSlot || '请选择时间段' }}
                </text>
                <!-- <text class="picker-arrow">></text> -->
              </view>
            </picker>
            <text v-if="errors.timeSlot" class="error-message">{{ errors.timeSlot }}</text>
          </view>
        </view>

        <view class="form-row">
          <view class="form-field">
            <view class="field-label">
              <text>备注信息</text>
              <text class="optional">(选填)</text>
            </view>
            <textarea
              class="field-textarea"
              v-model.trim="form.remarks"
              placeholder="请输入备注信息，如特殊要求、注意事项等"
              maxlength="200"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, defineProps, defineEmits, defineExpose } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const form = reactive({
  contactName: '',
  contactPhone: '',
  date: '',
  timeSlot: '',
  remarks: ''
})

const errors = reactive({
  contactName: '',
  contactPhone: '',
  date: '',
  timeSlot: '',
  remarks: ''
})

const timeSlots = ['上午 9:00-11:00', '下午 13:00-15:00', '下午 15:00-17:00']
const timeSlotIndex = ref(0)
const startDate = ref('')
const endDate = ref('')

// 计算属性
const formData = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

// 监听modelValue变化，处理重新预约的情况
watch(() => props.modelValue, (newValue, oldValue) => {
  // 如果从有预约状态变为无预约状态，说明是重新预约，需要重置表单
  if (oldValue?.appointment && !newValue?.appointment) {
    // 重置表单数据
    Object.assign(form, {
      contactName: '',
      contactPhone: '',
      date: '',
      timeSlot: '',
      remarks: ''
    })
    // 清除错误信息
    Object.keys(errors).forEach(key => {
      errors[key] = ''
    })
  } else if (newValue?.appointment) {
    // 如果有预约数据，填充表单（编辑模式）
    Object.assign(form, newValue.appointment)
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  // 设置日期范围
  const now = new Date()
  startDate.value = formatDate(now)

  const threeMonthsLater = new Date()
  threeMonthsLater.setMonth(now.getMonth() + 3)
  endDate.value = formatDate(threeMonthsLater)

  // 初始化表单数据
  if (props.modelValue.appointment) {
    Object.assign(form, props.modelValue.appointment)
  }
})

// 工具方法
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 事件处理
const onDateChange = (e) => {
  form.date = e.detail.value
  validateField('date')
  updateFormData()
}

const onTimeSlotChange = (e) => {
  timeSlotIndex.value = e.detail.value
  form.timeSlot = timeSlots[e.detail.value]
  validateField('timeSlot')
  updateFormData()
}

const updateFormData = () => {
  const updatedForm = {
    ...formData.value,
    appointment: { ...form }
  }
  emit('update:modelValue', updatedForm)
}

const validateField = (field) => {
  switch (field) {
    case 'contactName':
      if (!form.contactName.trim()) {
        errors.contactName = '请输入联系人姓名'
      } else if (form.contactName.length < 2) {
        errors.contactName = '联系人姓名至少2个字符'
      } else {
        errors.contactName = ''
      }
      break
      
    case 'contactPhone':
      const phoneReg = /^1[3-9]\d{9}$/
      if (!form.contactPhone.trim()) {
        errors.contactPhone = '请输入联系电话'
      } else if (!phoneReg.test(form.contactPhone)) {
        errors.contactPhone = '请输入有效的手机号码'
      } else {
        errors.contactPhone = ''
      }
      break
      
    case 'date':
      if (!form.date) {
        errors.date = '请选择预约日期'
      } else {
        errors.date = ''
      }
      break
      
    case 'timeSlot':
      if (!form.timeSlot) {
        errors.timeSlot = '请选择时间段'
      } else {
        errors.timeSlot = ''
      }
      break
  }
  
  // 更新父组件数据
  updateFormData()
}

const validateAll = () => {
  // 触发所有字段的验证
  validateField('contactName')
  validateField('contactPhone')
  validateField('date')
  validateField('timeSlot')
  
  // 检查是否有验证错误
  const hasErrors = Object.values(errors).some(error => error !== '')
  const hasEmptyFields = !form.contactName.trim() || !form.contactPhone.trim() || !form.date || !form.timeSlot
  
  if (hasErrors || hasEmptyFields) {
    // 显示第一个错误信息或提示完善信息
    if (hasErrors) {
      const firstError = Object.values(errors).find(error => error !== '')
      uni.showToast({
        title: firstError,
        icon: 'none'
      })
    } else {
      uni.showToast({
        title: '请完善预约信息',
        icon: 'none'
      })
    }
    return false
  }
  
  return true
}

const submitAppointment = async () => {
  // 验证所有字段
  if (!validateAll()) {
    throw new Error('表单验证失败')
  }

  uni.showLoading({
    title: '提交预约中...',
    mask: true
  })

  try {
    // 模拟网络请求
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 更新预约状态
    const appointmentData = {
      ...form,
      status: 'confirmed',
      appointmentId: 'APT' + Date.now(),
      createTime: new Date().toISOString()
    }

    const updatedForm = {
      ...formData.value,
      appointment: appointmentData
    }
    
    emit('update:modelValue', updatedForm)
    
    uni.hideLoading()
    uni.showToast({
      title: '预约提交成功',
      icon: 'success',
      duration: 2000
    })
    
    return Promise.resolve()
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '预约提交失败，请重试',
      icon: 'none'
    })
    throw error
  }
}

// 暴露给父组件的方法
defineExpose({
  validateAll,
  submitAppointment
})
</script>

<style scoped>
/* 基础样式 */
.appointment-form {
  padding: 10rpx;
}

.form-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx 30rpx 50rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.title-icon {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.title-icon .icon {
  font-size: 28rpx;
  color: #fff;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.form-row {
  display: flex;
  flex-direction: column;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.form-field.has-error .field-input,
.form-field.has-error .field-picker {
  border-color: #ff4d4f;
}

.field-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.required {
  color: #ff4d4f;
  font-size: 28rpx;
}

.optional {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

.field-input, .field-textarea {
  padding: 24rpx 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
  transition: border-color 0.3s ease;
}

.field-input:focus, .field-textarea:focus {
  border-color: #1890ff;
  outline: none;
}

.field-textarea {
  min-height: 120rpx;
  resize: none;
}

.field-picker {
  padding: 24rpx 20rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  background-color: #fff;
  transition: border-color 0.3s ease;
}

.picker-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.picker-content .placeholder {
  color: #999;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
}

.error-message {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 8rpx;
}
</style>
