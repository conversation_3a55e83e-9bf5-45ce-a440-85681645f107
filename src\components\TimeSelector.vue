<template>
  <view class="time-selector-section">
    <view class="time-selector-title">{{ title }}</view>
    <view class="delivery-container">
      <view class="date-selector">
        <view
          v-for="(date, index) in dates"
          :key="index"
          :class="['date-item', { active: selectedDateIndex === index }]"
          @click="selectDate(index)"
        >
          <view class="date-week">{{ date.week }}</view>
          <view class="date-day">{{ date.day }}</view>
        </view>
      </view>

      <view class="time-selector" v-if="selectedDateIndex !== null">
        <view class="time-title">选择时间段</view>
        <view class="time-options">
          <view
            v-for="(time, index) in timeSlots"
            :key="index"
            :class="['time-item', { active: selectedTimeIndex === index, disabled: !time.available }]"
            @click="selectTime(index, time.available)"
          >
            <view class="time-text">{{ time.text }}</view>
            <!-- <view class="time-price" v-if="time.extraFee > 0">+¥{{ time.extraFee }}</view> -->
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TimeSelector',
  props: {
    title: {
      type: String,
      default: '选择时间'
    },
    dates: {
      type: Array,
      required: true
    },
    timeSlots: {
      type: Array,
      required: true
    },
    selectedDateIndex: {
      type: Number,
      default: 0
    },
    selectedTimeIndex: {
      type: Number,
      default: 0
    }
  },
  methods: {
    selectDate(index) {
      this.$emit('date-change', index);
    },
    selectTime(index, available) {
      if (!available) return;
      this.$emit('time-change', index);
    }
  }
}
</script>

<style scoped>
/* 时间选择器容器 - 继承父组件的section样式 */

.time-selector-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  border-left: 8rpx solid #2979ff;
}

/* 配送时间样式 */
.delivery-container {
  display: flex;
  flex-direction: column;
}

.date-selector {
  display: flex;
  overflow-x: auto;
  margin-bottom: 30rpx;
  padding-bottom: 10rpx;
}

.date-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 120rpx;
  height: 100rpx;
  margin-right: 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.date-item.active {
  background-color: #2979ff;
  color: white;
  border-color: #2979ff;
}

.date-week {
  font-size: 24rpx;
  margin-bottom: 5rpx;
}

.date-day {
  font-size: 28rpx;
  font-weight: bold;
}

.time-selector {
  display: flex;
  flex-direction: column;
}

.time-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.time-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.time-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 160rpx;
  height: 80rpx;
  padding: 0 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s;
  position: relative;
}

.time-item.active {
  background-color: #2979ff;
  color: white;
  border-color: #2979ff;
}

.time-item.disabled {
  background-color: #f0f0f0;
  color: #ccc;
  pointer-events: none;
}

.time-text {
  font-size: 26rpx;
  font-weight: 500;
}

.time-price {
  font-size: 22rpx;
  color: #ff6700;
  margin-top: 5rpx;
}
</style>
