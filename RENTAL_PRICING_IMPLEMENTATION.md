# 气瓶租赁价格分离功能实现

## 功能概述

根据需求，在气瓶租赁业务中，需要将价格分为两部分：
1. **燃气价格**：实际消费的燃气费用，不可退还
2. **押金价格**：设备保证金，退瓶时可退还（扣除租金）

## 实现的功能

### 1. 订单页面 (gas-order.vue)

#### 价格规则说明功能
- 在"气瓶规格"标题旁添加了问号图标
- 点击问号图标弹出价格规则详情弹窗
- 弹窗包含完整的价格构成说明和退瓶规则

#### 数据结构修改
- 为每个气瓶规格添加了 `gasPrice` 和 `depositPrice` 字段
- 保留原有的 `price` 字段用于换气业务

```javascript
const specs = ref([
  {
    id: 1,
    name: "5kg气瓶",
    netWeight: "4kg",
    price: 70,        // 换气价格
    gasPrice: 50,     // 燃气价格（租赁时显示）
    depositPrice: 20, // 押金价格（租赁时显示）
    image: "/static/images/5kg.jpg",
    quantity: 0
  },
  // ... 其他规格
])
```

#### 界面显示
- **换气模式**：显示总价格 `¥70`
- **租赁模式**：分别显示燃气价格和押金价格
  ```
  燃气价格：¥50
  押金：¥20
  ```

#### 价格计算
- **换气模式**：使用 `price` 字段
- **租赁模式**：使用 `gasPrice + depositPrice`

#### 订单数据
- 租赁模式下，订单数据分别记录燃气价格和押金价格
- 便于后续退款时区分可退和不可退的费用

### 2. 退瓶退款页面 (bottle-return.vue)

#### 费用显示
- **燃气费用**：显示为"不可退"状态
- **押金金额**：可退还金额
- **租金费用**：从押金中扣除的费用
- **应退金额**：最终可退还的金额（仅押金部分）

#### 界面布局
```
费用结算
├── 燃气费用: ¥50 (不可退)
├── 押金金额: +¥20
├── 租金费用: -¥5.5
└── 应退金额（仅押金）: +¥14.5
```

#### 说明信息
- 燃气费用为消费性支出，不可退还
- 押金将在确认设备无损后的1-3个工作日内退回您的账户

## 价格配置

| 规格 | 换气价格 | 燃气价格 | 押金价格 |
|------|----------|----------|----------|
| 5kg  | ¥70     | ¥50      | ¥20      |
| 15kg | ¥130    | ¥100     | ¥30      |
| 50kg | ¥380    | ¥320     | ¥60      |

## 业务逻辑

### 租赁流程
1. 用户选择租赁业务
2. 选择气瓶规格和数量
3. 支付总金额（燃气价格 + 押金价格）
4. 获得气瓶使用权

### 退瓶流程
1. 用户扫码退瓶
2. 系统计算租金费用
3. 从押金中扣除租金
4. 退还剩余押金
5. 燃气费用不退还

## 测试页面

创建了测试页面验证功能：

1. **test-pricing-rules.html**：测试价格规则弹窗
   - 验证问号图标点击功能
   - 测试弹窗显示和关闭
   - 验证价格规则内容展示
   - 支持ESC键和遮罩点击关闭

## 技术实现要点

### 1. 响应式价格显示
使用 Vue 3 的响应式系统，根据业务类型动态显示不同的价格结构。

### 2. 计算属性优化
通过计算属性实现价格的自动计算和更新，确保数据一致性。

### 3. 样式区分
使用不同的颜色区分燃气价格（橙色）和押金价格（蓝色），提高用户体验。

### 4. 数据结构兼容
保持向后兼容，换气业务仍使用原有的价格字段。

### 5. 用户体验优化
- 添加帮助图标，提供价格规则说明
- 弹窗设计与页面风格统一，保持一致的视觉体验
- 使用页面相同的颜色规范和字体大小
- 支持多种关闭方式（按钮、遮罩点击）
- 修复了 `pointer-events: none` 导致的点击问题

## 后续扩展

1. **动态价格配置**：可从后端API获取价格配置
2. **租金计算规则**：可配置不同的租金计算方式
3. **退款策略**：可根据不同情况制定退款策略
4. **价格历史**：记录价格变更历史，便于审计

## 注意事项

1. 确保前端显示的价格与后端计算一致
2. 退款时需要验证设备状态，异常设备可能产生额外费用
3. 价格配置变更时需要考虑已有订单的处理
4. 需要完善的日志记录，便于问题排查
