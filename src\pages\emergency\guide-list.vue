<template>
  <view class="container">
    <view class="header">
      <view class="header-title">应急指南</view>
      <view class="header-desc">燃气安全知识大全</view>
    </view>
    
    <view class="search-bar">
      <view class="search-input">
        <uni-icons type="search" size="18" color="#999"></uni-icons>
        <input type="text" placeholder="搜索应急指南" v-model="searchKeyword" @input="onSearch" />
      </view>
    </view>
    

    
    <scroll-view class="guide-list" scroll-y>
      <view class="guide-item" v-for="guide in filteredGuides" :key="guide.id" @click="showGuideDetail(guide)">
        <view class="guide-icon">
          <image :src="guide.icon" mode="aspectFit"></image>
        </view>
        <view class="guide-content">
          <view class="guide-title">{{guide.title}}</view>
          <view class="guide-desc">{{guide.desc}}</view>
          <view class="guide-meta">
            <text class="guide-time">{{guide.updateTime}}</text>
          </view>
        </view>
        <view class="guide-arrow">
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
      </view>
    </scroll-view>
    
    <!-- 抽屉式详情 -->
    <uni-popup ref="popup" type="bottom" @change="drawerChange" :safe-area="false" background-color="#fff">
      <view class="drawer-content">
        <view class="drawer-header">
          <view class="drawer-title">{{currentGuide.title || '应急指南详情'}}</view>
          <view class="drawer-close" @click="closeDrawer">
            <text class="close-icon">×</text>
          </view>
        </view>
        <scroll-view class="drawer-body" scroll-y>
          <view class="guide-detail">
            <view class="detail-icon" v-if="currentGuide.icon">
              <image :src="currentGuide.icon" mode="aspectFit"></image>
            </view>
            <view class="detail-content" v-html="currentGuide.content || '暂无详细内容'"></view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'

const popup = ref()
const searchKeyword = ref('')
const currentGuide = ref({})

const allGuides = ref([
        {
          id: 1,
          title: '燃气泄漏应急处理',
          desc: '如何正确处理家庭燃气泄漏情况',
          icon: '/static/images/emergency/gas-leak.png',
          updateTime: '2024-01-15',
          content: `
            <h3>燃气泄漏应急处理步骤</h3>
            <p><strong>1. 立即关闭燃气阀门</strong></p>
            <p>发现燃气泄漏后，首先要保持冷静，立即关闭燃气表前的总阀门或钢瓶角阀。</p>
            
            <p><strong>2. 开窗通风</strong></p>
            <p>迅速打开门窗，让空气流通，稀释泄漏的燃气浓度。</p>
            
            <p><strong>3. 严禁明火和电器操作</strong></p>
            <p>不要开关电灯、电器，不要使用打火机、火柴等，避免产生火花引起爆炸。</p>
            
            <p><strong>4. 疏散人员</strong></p>
            <p>立即疏散现场人员到安全地带，特别是老人和儿童。</p>
            
            <p><strong>5. 报警求助</strong></p>
            <p>在安全地带拨打燃气公司抢修电话或119报警电话。</p>
          `
        },
        {
          id: 2,
          title: '燃气火灾应对',
          desc: '燃气火灾的应急自救措施',
          icon: '/static/images/emergency/fire.png',
          updateTime: '2024-01-12',
          content: `
            <h3>燃气火灾应急处理</h3>
            <p><strong>1. 立即切断气源</strong></p>
            <p>如果可以安全接近，立即关闭燃气阀门，切断气源。</p>
            
            <p><strong>2. 使用正确的灭火方法</strong></p>
            <p>使用干粉灭火器、二氧化碳灭火器或沙土灭火，严禁用水扑救燃气火灾。</p>
            
            <p><strong>3. 疏散逃生</strong></p>
            <p>火势较大时，立即疏散人员，沿安全通道快速撤离。</p>
            
            <p><strong>4. 报警求助</strong></p>
            <p>立即拨打119火警电话，详细说明火灾地点和燃气泄漏情况。</p>
            
            <p><strong>5. 等待专业救援</strong></p>
            <p>在安全地带等待消防队员到达，配合救援工作。</p>
          `
        },
        {
          id: 3,
          title: '燃气中毒急救',
          desc: '燃气中毒的症状与应急处理方法',
          icon: '/static/images/emergency/first-aid.png',
          updateTime: '2024-01-10',
          content: `
            <h3>燃气中毒急救处理</h3>
            <p><strong>中毒症状识别：</strong></p>
            <p>头晕、头痛、恶心、呕吐、乏力、意识模糊等。</p>
            
            <p><strong>1. 迅速脱离现场</strong></p>
            <p>立即将中毒者转移到空气新鲜的地方，解开衣领，保持呼吸道畅通。</p>
            
            <p><strong>2. 保持呼吸道畅通</strong></p>
            <p>让患者平躺，头部偏向一侧，防止呕吐物堵塞呼吸道。</p>
            
            <p><strong>3. 人工呼吸</strong></p>
            <p>如患者呼吸停止，立即进行人工呼吸和心肺复苏。</p>
            
            <p><strong>4. 立即就医</strong></p>
            <p>拨打120急救电话，将患者送往医院救治。</p>
            
            <p><strong>5. 现场通风</strong></p>
            <p>同时处理泄漏源，开窗通风，防止更多人中毒。</p>
          `
        },
        {
          id: 4,
          title: '燃气设备日常维护',
          desc: '燃气设备的日常检查与维护要点',
          icon: '/static/images/emergency/maintenance.png',
          updateTime: '2024-01-08',
          content: `
            <h3>燃气设备日常维护</h3>
            <p><strong>1. 定期检查燃气管道</strong></p>
            <p>每月检查一次燃气管道接头，用肥皂水检查是否有泄漏。</p>

            <p><strong>2. 清洁燃气灶具</strong></p>
            <p>定期清洁灶具表面和燃烧器，保持通风孔畅通。</p>

            <p><strong>3. 检查燃气软管</strong></p>
            <p>软管使用期限不超过2年，发现老化、龟裂应及时更换。</p>

            <p><strong>4. 燃气表维护</strong></p>
            <p>保持燃气表周围清洁，不要在表箱内堆放杂物。</p>

            <p><strong>5. 专业检测</strong></p>
            <p>每年请专业人员进行一次全面检测和维护。</p>
          `
        },
        {
          id: 5,
          title: '家庭燃气安全检查',
          desc: '家庭燃气使用安全检查清单',
          icon: '/static/images/emergency/safety-check.png',
          updateTime: '2024-01-05',
          content: `
            <h3>家庭燃气安全检查清单</h3>
            <p><strong>每日检查：</strong></p>
            <p>• 使用后关闭灶具开关和燃气阀门</p>
            <p>• 检查厨房通风是否良好</p>

            <p><strong>每周检查：</strong></p>
            <p>• 检查燃气软管是否有松动</p>
            <p>• 清洁灶具表面和燃烧器</p>

            <p><strong>每月检查：</strong></p>
            <p>• 用肥皂水检查管道接头</p>
            <p>• 检查燃气表是否正常</p>

            <p><strong>每年检查：</strong></p>
            <p>• 更换老化的燃气软管</p>
            <p>• 请专业人员全面检测</p>
          `
        },
        {
          id: 6,
          title: '燃气瓶安全使用',
          desc: '液化气瓶的正确使用和存放方法',
          icon: '/static/images/emergency/gas-bottle.png',
          updateTime: '2024-01-03',
          content: `
            <h3>燃气瓶安全使用指南</h3>
            <p><strong>1. 正确连接</strong></p>
            <p>使用专用减压阀和软管，连接时要拧紧，不能漏气。</p>

            <p><strong>2. 安全存放</strong></p>
            <p>燃气瓶应直立存放，远离热源，不能倒置或横放。</p>

            <p><strong>3. 使用注意事项</strong></p>
            <p>使用时先开气瓶阀门，再开灶具；关闭时先关灶具，再关气瓶。</p>

            <p><strong>4. 定期检查</strong></p>
            <p>检查减压阀、软管是否老化，气瓶是否在检验期内。</p>

            <p><strong>5. 更换气瓶</strong></p>
            <p>更换气瓶时要关闭所有阀门，在通风良好的地方进行。</p>
          `
        }
])

// 计算属性
const filteredGuides = computed(() => {
  let guides = allGuides.value

  // 按搜索关键词筛选
  if (searchKeyword.value) {
    guides = guides.filter(guide =>
      guide.title.includes(searchKeyword.value) ||
      guide.desc.includes(searchKeyword.value)
    )
  }

  return guides
})

// 搜索
const onSearch = () => {
  // 实际应用中可以添加防抖处理
}

// 显示指南详情
const showGuideDetail = (guide) => {
  currentGuide.value = guide
  popup.value.open('bottom')
}

// 关闭抽屉
const closeDrawer = () => {
  popup.value.close()
}

// 抽屉状态改变
const drawerChange = (e) => {
  if (!e.show) {
    currentGuide.value = {}
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
}

.header {
  height: 200rpx;
  background: linear-gradient(to right, #ff5050, #ff7676);
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.header-desc {
  font-size: 28rpx;
  opacity: 0.8;
}

.search-bar {
  padding: 30rpx 20rpx;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
}

.search-input {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 25rpx;
  padding: 20rpx 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.search-input input {
  flex: 1;
  margin-left: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}



.guide-list {
  flex: 1;
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.guide-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.05);
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.guide-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  box-sizing: border-box;
}

.guide-icon image {
  width: 100%;
  height: 100%;
}

.guide-content {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.guide-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.guide-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 15rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.guide-meta {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.guide-time {
  font-size: 22rpx;
  color: #ccc;
}

/* 抽屉内容样式 */
.drawer-content {
  width: 100%;
  height: 80vh;
  background-color: #fff !important;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

/* 小程序端特殊处理 */
/* #ifdef MP-WEIXIN */
.drawer-content {
  background: #fff !important;
  border-top-left-radius: 30rpx !important;
  border-top-right-radius: 30rpx !important;
}

.drawer-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  z-index: -1;
}
/* #endif */

/* uni-popup 组件样式覆盖 */
:deep(.uni-popup__wrapper-box) {
  border-top-left-radius: 30rpx !important;
  border-top-right-radius: 30rpx !important;
  overflow: hidden;
}

:deep(.uni-popup-bottom) {
  border-top-left-radius: 30rpx !important;
  border-top-right-radius: 30rpx !important;
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.drawer-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.drawer-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.close-icon {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.drawer-close:active {
  transform: scale(0.95);
  background-color: #e5e5e5;
}

.drawer-body {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
  background-color: #fff;
}

.guide-detail {
  line-height: 1.6;
  background-color: #fff;
}

.detail-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 30rpx;
  display: flex;
  justify-content: center;
}

.detail-icon image {
  width: 100%;
  height: 100%;
}

.detail-content {
  font-size: 28rpx;
  color: #333;
}

.detail-content h3 {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #2979ff;
}

.detail-content p {
  margin-bottom: 15rpx;
  line-height: 1.8;
}

.detail-content strong {
  color: #fa3534;
  font-weight: bold;
}
</style>
