<template>
  <view class="container">
    <!-- 头部统计 -->
    <view class="header-stats">
      <view class="stat-item">
        <view class="stat-number">{{ taskStats.total }}</view>
        <view class="stat-label">总任务</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ taskStats.install }}</view>
        <view class="stat-label">安装</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ taskStats.repair }}</view>
        <view class="stat-label">维修</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ taskStats.completed }}</view>
        <view class="stat-label">已完成</view>
      </view>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <view 
        v-for="(tab, index) in filterTabs" 
        :key="index"
        :class="['tab-item', { active: activeTab === tab.value }]"
        @click="switchTab(tab.value)"
      >
        {{ tab.label }}
      </view>
    </view>

    <!-- 任务列表 -->
    <view class="task-list">
      <view 
        v-for="task in filteredTasks" 
        :key="task.id"
        class="task-item"
        @click="goToTaskDetail(task.id)"
      >
        <!-- 任务头部 -->
        <view class="task-header">
          <view class="task-info">
            <view class="task-title">{{ task.title }}</view>
            <view class="task-order">订单号：{{ task.orderNo }}</view>
          </view>
          <view class="task-status" :class="`status-${task.currentStep}`">
            {{ getStepText(task.currentStep) }}
          </view>
        </view>

        <!-- 客户信息 -->
        <view class="customer-info">
          <view class="customer-item">
            <text class="label">客户：</text>
            <text class="value">{{ task.customerName }}</text>
          </view>
          <view class="customer-item">
            <text class="label">地址：</text>
            <text class="value">{{ task.address }}</text>
          </view>
          <view class="customer-item">
            <text class="label">预约时间：</text>
            <text class="value">{{ task.appointmentTime }}</text>
          </view>
        </view>

        <!-- 任务进度 -->
        <view class="task-progress">
          <view class="progress-bar">
            <view 
              class="progress-fill" 
              :style="{ width: getProgressPercent(task) + '%' }"
            ></view>
          </view>
          <view class="progress-text">
            {{ getCompletedSteps(task) }}/{{ task.totalSteps }} 步骤完成
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="task-actions">
          <button 
            v-if="task.currentStep === 'accepted'" 
            class="btn btn-primary" 
            @click.stop="startTask(task.id)"
          >
            前往现场
          </button>
          <button 
            v-if="task.currentStep === 'onWay'" 
            class="btn btn-primary" 
            @click.stop="arriveTask(task.id)"
          >
            到达现场
          </button>
          <button 
            v-if="task.currentStep === 'working'" 
            class="btn btn-primary" 
            @click.stop="uploadMaterials(task.id)"
          >
            上传资料
          </button>
          <button 
            v-if="task.currentStep === 'uploaded'" 
            class="btn btn-outline" 
            @click.stop="viewMaterials(task.id)"
          >
            查看资料
          </button>
          <button 
            v-if="task.currentStep === 'approved'" 
            class="btn btn-primary" 
            @click.stop="completeTask(task.id)"
          >
            完成作业
          </button>
          <button 
            v-if="task.currentStep === 'finished'" 
            class="btn btn-primary" 
            @click.stop="requestSignature(task.id)"
          >
            客户签名
          </button>
          <button 
            class="btn btn-outline" 
            @click.stop="contactCustomer(task.customerPhone)"
          >
            联系客户
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="filteredTasks.length === 0">
      <view class="empty-icon">📋</view>
      <text class="empty-text">暂无相关任务</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const activeTab = ref('all')
const taskList = ref([])

// 筛选标签
const filterTabs = ref([
  { label: '全部', value: 'all' },
  { label: '安装', value: 'install' },
  { label: '维修', value: 'repair' },
  { label: '进行中', value: 'inProgress' },
  { label: '已完成', value: 'completed' }
])

// 任务统计
const taskStats = computed(() => {
  const total = taskList.value.length
  const install = taskList.value.filter(task => task.type === 'install').length
  const repair = taskList.value.filter(task => task.type === 'repair').length
  const completed = taskList.value.filter(task => task.currentStep === 'signed').length

  return { total, install, repair, completed }
})

// 筛选后的任务
const filteredTasks = computed(() => {
  if (activeTab.value === 'all') {
    return taskList.value
  } else if (activeTab.value === 'install') {
    return taskList.value.filter(task => task.type === 'install')
  } else if (activeTab.value === 'repair') {
    return taskList.value.filter(task => task.type === 'repair')
  } else if (activeTab.value === 'inProgress') {
    return taskList.value.filter(task =>
      ['accepted', 'onWay', 'working', 'uploaded', 'approved', 'finished'].includes(task.currentStep)
    )
  } else if (activeTab.value === 'completed') {
    return taskList.value.filter(task => task.currentStep === 'signed')
  }
  return taskList.value
})

// 页面加载
onMounted(() => {
  loadTaskList()
})

// 加载任务列表
const loadTaskList = () => {
  uni.showLoading({
    title: '加载中'
  })

  // 模拟API请求
  setTimeout(() => {
    taskList.value = [
      {
        id: 1,
        title: '燃气报警器安装',
        type: 'install',
        orderNo: 'GAS202401001',
        customerName: '张先生',
        customerPhone: '13800138001',
        address: '广州市海珠区新港东路123号',
        appointmentTime: '2024-01-15 09:00-11:00',
        currentStep: 'working',
        totalSteps: 8,
        completedSteps: 3
      },
      {
        id: 2,
        title: '燃气灶安装',
        type: 'install',
        orderNo: 'GAS202401002',
        customerName: '李女士',
        customerPhone: '13800138002',
        address: '广州市天河区珠江新城88号',
        appointmentTime: '2024-01-15 14:00-16:00',
        currentStep: 'uploaded',
        totalSteps: 8,
        completedSteps: 4
      },
      {
        id: 3,
        title: '燃气管道维修',
        type: 'repair',
        orderNo: 'GAS202401003',
        customerName: '王先生',
        customerPhone: '13800138003',
        address: '广州市越秀区中山路456号',
        appointmentTime: '2024-01-16 10:00-12:00',
        currentStep: 'pending',
        totalSteps: 8,
        completedSteps: 0
      },
      {
        id: 4,
        title: '燃气报警器维修',
        type: 'repair',
        orderNo: 'GAS202401004',
        customerName: '陈女士',
        customerPhone: '13800138004',
        address: '广州市白云区机场路789号',
        appointmentTime: '2024-01-14 15:00-17:00',
        currentStep: 'signed',
        totalSteps: 8,
        completedSteps: 8
      },
      {
        id: 5,
        title: '燃气热水器维修',
        type: 'repair',
        orderNo: 'GAS202401005',
        customerName: '刘先生',
        customerPhone: '13800138005',
        address: '广州市番禺区市桥路321号',
        appointmentTime: '2024-01-16 14:00-16:00',
        currentStep: 'accepted',
        totalSteps: 8,
        completedSteps: 1
      },
      {
        id: 6,
        title: '燃气表安装',
        type: 'install',
        orderNo: 'GAS202401006',
        customerName: '周女士',
        customerPhone: '13800138006',
        address: '广州市花都区新华路654号',
        appointmentTime: '2024-01-17 09:00-11:00',
        currentStep: 'finished',
        totalSteps: 8,
        completedSteps: 7
      }
    ]

    uni.hideLoading()
  }, 1000)
}

// 切换标签
const switchTab = (tabValue) => {
  activeTab.value = tabValue
}

// 获取步骤文本
const getStepText = (step) => {
  const stepMap = {
    'pending': '待接受',
    'accepted': '已接受',
    'onWay': '前往中',
    'working': '作业中',
    'uploaded': '待审核',
    'approved': '审核通过',
    'finished': '待签名',
    'signed': '已完成'
  }
  return stepMap[step] || '未知状态'
}

// 获取进度百分比
const getProgressPercent = (task) => {
  return Math.round((task.completedSteps / task.totalSteps) * 100)
}

// 获取已完成步骤数
const getCompletedSteps = (task) => {
  return task.completedSteps
}

// 跳转任务详情
const goToTaskDetail = (taskId) => {
  uni.navigateTo({
    url: `/pages/task/task-detail?id=${taskId}`
  })
}

// 开始任务
const startTask = (taskId) => {
  uni.showToast({
    title: '开始前往现场',
    icon: 'success'
  })
}

// 到达现场
const arriveTask = (taskId) => {
  uni.showToast({
    title: '已到达现场',
    icon: 'success'
  })
}

// 上传资料
const uploadMaterials = (taskId) => {
  uni.navigateTo({
    url: `/pages/task/upload-materials?id=${taskId}`
  })
}

// 查看资料
const viewMaterials = (taskId) => {
  uni.navigateTo({
    url: `/pages/task/view-materials?id=${taskId}`
  })
}

// 完成任务
const completeTask = (taskId) => {
  uni.showToast({
    title: '任务已完成',
    icon: 'success'
  })
}

// 请求客户签名
const requestSignature = (taskId) => {
  uni.navigateTo({
    url: `/pages/task/signature?id=${taskId}`
  })
}

// 联系客户
const contactCustomer = (phone) => {
  uni.makePhoneCall({
    phoneNumber: phone
  })
}
</script>

<style scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 头部统计 */
.header-stats {
  display: flex;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 20rpx;
  color: #fff;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background-color: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 24rpx;
  margin: 0 8rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 8rpx;
  background-color: transparent;
  transition: all 0.2s ease;
}

.tab-item.active {
  color: #2979ff;
  background-color: #f0f7ff;
  font-weight: 500;
}

/* 任务列表 */
.task-list {
  padding: 20rpx;
}

.task-item {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
}

.task-item:active {
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.08);
}

/* 任务头部 */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.task-info {
  flex: 1;
}

.task-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.task-order {
  font-size: 24rpx;
  color: #999;
}

.task-status {
  font-size: 22rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 4rpx;
  white-space: nowrap;
}

.status-pending {
  color: #ff9500;
  background-color: #fff5e6;
}

.status-accepted, .status-onWay {
  color: #1890ff;
  background-color: #e6f7ff;
}

.status-working, .status-uploaded {
  color: #722ed1;
  background-color: #f9f0ff;
}

.status-approved, .status-finished {
  color: #52c41a;
  background-color: #f6ffed;
}

.status-signed {
  color: #8c8c8c;
  background-color: #f5f5f5;
}

/* 客户信息 */
.customer-info {
  margin-bottom: 16rpx;
}

.customer-item {
  display: flex;
  margin-bottom: 8rpx;
  font-size: 24rpx;
}

.customer-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #999;
  min-width: 120rpx;
}

.value {
  color: #333;
  flex: 1;
}

/* 任务进度 */
.task-progress {
  margin-bottom: 20rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 22rpx;
  color: #666;
  text-align: right;
}

/* 操作按钮 */
.task-actions {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.btn {
  padding: 12rpx 20rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  border: none;
  transition: all 0.2s ease;
  white-space: nowrap;
  font-weight: 400;
}

.btn-outline {
  background-color: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.btn-outline:active {
  background-color: #e9ecef;
}

.btn-primary {
  background-color: #2979ff;
  color: #fff;
}

.btn-primary:active {
  background-color: #1976d2;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 26rpx;
  color: #999;
  text-align: center;
}
</style>
