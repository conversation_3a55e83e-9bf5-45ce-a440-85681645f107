<template>
  <view class="container">
    <!-- 头部统计 -->
    <view class="header-stats">
      <view class="stat-item">
        <view class="stat-number">{{ taskStats.total }}</view>
        <view class="stat-label">总配送</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ taskStats.pending }}</view>
        <view class="stat-label">待配送</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ taskStats.inProgress }}</view>
        <view class="stat-label">配送中</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ taskStats.completed }}</view>
        <view class="stat-label">已完成</view>
      </view>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <view 
        v-for="(tab, index) in filterTabs" 
        :key="index"
        :class="['tab-item', { active: activeTab === tab.value }]"
        @click="switchTab(tab.value)"
      >
        {{ tab.label }}
      </view>
    </view>

    <!-- 配送任务列表 -->
    <view class="task-list">
      <view 
        v-for="task in filteredTasks" 
        :key="task.id"
        class="task-item"
        @click="goToTaskDetail(task.id)"
      >
        <!-- 任务头部 -->
        <view class="task-header">
          <view class="task-info">
            <view class="task-title">{{ task.title }}</view>
            <view class="task-order">订单号：{{ task.orderNo }}</view>
          </view>
          <view class="task-status" :class="`status-${task.currentStep}`">
            {{ getStepText(task.currentStep) }}
          </view>
        </view>

        <!-- 客户信息 -->
        <view class="customer-info">
          <view class="customer-item">
            <text class="label">客户：</text>
            <text class="value">{{ task.customerName }}</text>
          </view>
          <view class="customer-item">
            <text class="label">地址：</text>
            <text class="value">{{ task.address }}</text>
          </view>
          <view class="customer-item">
            <text class="label">配送时间：</text>
            <text class="value">{{ task.deliveryTime }}</text>
          </view>
          <view class="customer-item" v-if="task.currentLocation">
            <text class="label">当前位置：</text>
            <text class="value">{{ task.currentLocation }}</text>
          </view>
        </view>

        <!-- 商品信息 -->
        <view class="goods-info">
          <view class="goods-title">配送商品</view>
          <view class="goods-list">
            <view v-for="(item, index) in task.items" :key="index" class="goods-item">
              <text class="goods-name">{{ item.name }}</text>
              <text class="goods-quantity">x{{ item.quantity }}</text>
            </view>
          </view>
        </view>

        <!-- 配送进度 -->
        <view class="task-progress">
          <view class="progress-bar">
            <view 
              class="progress-fill" 
              :style="{ width: getProgressPercent(task) + '%' }"
            ></view>
          </view>
          <view class="progress-text">
            {{ getCompletedSteps(task) }}/{{ task.totalSteps }} 步骤完成
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="task-actions">
          <button 
            v-if="task.currentStep === 'accepted'" 
            class="btn btn-primary" 
            @click.stop="startDelivery(task.id)"
          >
            开始配送
          </button>
          <button 
            v-if="task.currentStep === 'outbound'" 
            class="btn btn-primary" 
            @click.stop="startRoute(task.id)"
          >
            出发配送
          </button>
          <button 
            v-if="task.currentStep === 'onWay'" 
            class="btn btn-primary" 
            @click.stop="updateLocation(task.id)"
          >
            更新位置
          </button>
          <button 
            v-if="task.currentStep === 'arriving'" 
            class="btn btn-primary" 
            @click.stop="confirmDelivery(task.id)"
          >
            确认送达
          </button>
          <button 
            class="btn btn-outline" 
            @click.stop="contactCustomer(task.customerPhone)"
          >
            联系客户
          </button>
          <button 
            v-if="['onWay', 'arriving'].includes(task.currentStep)" 
            class="btn btn-outline" 
            @click.stop="viewRoute(task.id)"
          >
            查看路线
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="filteredTasks.length === 0">
      <view class="empty-icon">🚚</view>
      <text class="empty-text">暂无配送任务</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const activeTab = ref('all')
const taskList = ref([])

// 筛选标签
const filterTabs = ref([
  { label: '全部', value: 'all' },
  { label: '待配送', value: 'pending' },
  { label: '配送中', value: 'inProgress' },
  { label: '已完成', value: 'completed' }
])

// 任务统计
const taskStats = computed(() => {
  const total = taskList.value.length
  const pending = taskList.value.filter(task => task.currentStep === 'pending').length
  const inProgress = taskList.value.filter(task => 
    ['accepted', 'outbound', 'onWay', 'arriving'].includes(task.currentStep)
  ).length
  const completed = taskList.value.filter(task => task.currentStep === 'delivered').length
  
  return { total, pending, inProgress, completed }
})

// 筛选后的任务
const filteredTasks = computed(() => {
  if (activeTab.value === 'all') {
    return taskList.value
  } else if (activeTab.value === 'pending') {
    return taskList.value.filter(task => task.currentStep === 'pending')
  } else if (activeTab.value === 'inProgress') {
    return taskList.value.filter(task => 
      ['accepted', 'outbound', 'onWay', 'arriving'].includes(task.currentStep)
    )
  } else if (activeTab.value === 'completed') {
    return taskList.value.filter(task => task.currentStep === 'delivered')
  }
  return taskList.value
})

// 页面加载
onMounted(() => {
  loadTaskList()
})

// 加载任务列表
const loadTaskList = () => {
  uni.showLoading({
    title: '加载中'
  })

  // 模拟API请求
  setTimeout(() => {
    taskList.value = [
      {
        id: 1,
        title: '气瓶配送',
        orderNo: 'GAS202401001',
        customerName: '张先生',
        customerPhone: '13800138001',
        address: '广州市海珠区新港东路123号',
        deliveryTime: '2024-01-15 09:00-11:00',
        currentStep: 'onWay',
        currentLocation: '距离目的地约2公里',
        totalSteps: 5,
        completedSteps: 3,
        items: [
          { name: '5kg气瓶', quantity: 2 },
          { name: '15kg气瓶', quantity: 1 }
        ]
      },
      {
        id: 2,
        title: '气瓶配送',
        orderNo: 'GAS202401002',
        customerName: '李女士',
        customerPhone: '13800138002',
        address: '广州市天河区珠江新城88号',
        deliveryTime: '2024-01-15 14:00-16:00',
        currentStep: 'arriving',
        currentLocation: '距离目的地约500米',
        totalSteps: 5,
        completedSteps: 4,
        items: [
          { name: '50kg气瓶', quantity: 1 }
        ]
      },
      {
        id: 3,
        title: '气瓶配送',
        orderNo: 'GAS202401003',
        customerName: '王先生',
        customerPhone: '13800138003',
        address: '广州市越秀区中山路456号',
        deliveryTime: '2024-01-16 10:00-12:00',
        currentStep: 'pending',
        totalSteps: 5,
        completedSteps: 0,
        items: [
          { name: '15kg气瓶', quantity: 2 }
        ]
      },
      {
        id: 4,
        title: '气瓶配送',
        orderNo: 'GAS202401004',
        customerName: '陈女士',
        customerPhone: '13800138004',
        address: '广州市白云区机场路789号',
        deliveryTime: '2024-01-14 15:00-17:00',
        currentStep: 'delivered',
        totalSteps: 5,
        completedSteps: 5,
        items: [
          { name: '5kg气瓶', quantity: 1 },
          { name: '燃气报警器', quantity: 1 }
        ]
      }
    ]

    uni.hideLoading()
  }, 1000)
}

// 切换标签
const switchTab = (tabValue) => {
  activeTab.value = tabValue
}

// 获取步骤文本
const getStepText = (step) => {
  const stepMap = {
    'pending': '待接单',
    'accepted': '已接单',
    'outbound': '商品出库',
    'onWay': '配送中',
    'arriving': '即将到达',
    'delivered': '已送达'
  }
  return stepMap[step] || '未知状态'
}

// 获取进度百分比
const getProgressPercent = (task) => {
  return Math.round((task.completedSteps / task.totalSteps) * 100)
}

// 获取已完成步骤数
const getCompletedSteps = (task) => {
  return task.completedSteps
}

// 跳转任务详情
const goToTaskDetail = (taskId) => {
  uni.navigateTo({
    url: `/pages/task/delivery-detail?id=${taskId}`
  })
}

// 开始配送
const startDelivery = (taskId) => {
  uni.showToast({
    title: '开始配送',
    icon: 'success'
  })
}

// 出发配送
const startRoute = (taskId) => {
  uni.showToast({
    title: '开始出发',
    icon: 'success'
  })
}

// 更新位置
const updateLocation = (taskId) => {
  uni.showToast({
    title: '位置已更新',
    icon: 'success'
  })
}

// 确认送达
const confirmDelivery = (taskId) => {
  uni.showToast({
    title: '确认送达',
    icon: 'success'
  })
}

// 联系客户
const contactCustomer = (phone) => {
  uni.makePhoneCall({
    phoneNumber: phone
  })
}

// 查看路线
const viewRoute = (taskId) => {
  uni.showToast({
    title: '查看配送路线',
    icon: 'none'
  })
}
</script>

<style scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 头部统计 */
.header-stats {
  display: flex;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  padding: 40rpx 20rpx;
  color: #fff;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background-color: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 24rpx;
  margin: 0 8rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 8rpx;
  background-color: transparent;
  transition: all 0.2s ease;
}

.tab-item.active {
  color: #52c41a;
  background-color: #f6ffed;
  font-weight: 500;
}

/* 任务列表 */
.task-list {
  padding: 20rpx;
}

.task-item {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
}

.task-item:active {
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.08);
}

/* 任务头部 */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.task-info {
  flex: 1;
}

.task-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
}

.task-order {
  font-size: 24rpx;
  color: #999;
}

.task-status {
  font-size: 22rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 4rpx;
  white-space: nowrap;
}

.status-pending {
  color: #ff9500;
  background-color: #fff5e6;
}

.status-accepted, .status-outbound {
  color: #1890ff;
  background-color: #e6f7ff;
}

.status-onWay, .status-arriving {
  color: #52c41a;
  background-color: #f6ffed;
}

.status-delivered {
  color: #8c8c8c;
  background-color: #f5f5f5;
}

/* 客户信息 */
.customer-info {
  margin-bottom: 16rpx;
}

.customer-item {
  display: flex;
  margin-bottom: 8rpx;
  font-size: 24rpx;
}

.customer-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #999;
  min-width: 120rpx;
}

.value {
  color: #333;
  flex: 1;
}

/* 商品信息 */
.goods-info {
  margin-bottom: 16rpx;
  padding: 16rpx;
  background-color: #fafafa;
  border-radius: 8rpx;
}

.goods-title {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.goods-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.goods-item {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
}

.goods-name {
  color: #333;
}

.goods-quantity {
  color: #666;
}

/* 任务进度 */
.task-progress {
  margin-bottom: 20rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a, #73d13d);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 22rpx;
  color: #666;
  text-align: right;
}

/* 操作按钮 */
.task-actions {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.btn {
  padding: 12rpx 20rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  border: none;
  transition: all 0.2s ease;
  white-space: nowrap;
  font-weight: 400;
}

.btn-outline {
  background-color: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.btn-outline:active {
  background-color: #e9ecef;
}

.btn-primary {
  background-color: #52c41a;
  color: #fff;
}

.btn-primary:active {
  background-color: #389e0d;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 26rpx;
  color: #999;
  text-align: center;
}
</style>
