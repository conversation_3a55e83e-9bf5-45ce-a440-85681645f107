<template>
  <view class="container">
    <view class="emergency-header">
      <view class="header-title">应急中心</view>
      <view class="header-desc">燃气安全，生命至上</view>
    </view>
    
    <view class="alarm-panel">
      <button class="sos-btn" @click="triggerAlarm">一键报警</button>
      <view class="alarm-desc">紧急情况下点击上方按钮，系统将通知紧急联系人并共享您的位置</view>
    </view>
    
    <view class="alarm-panel">
      <button class="repair-btn" @click="callRepair">一键报修</button>
      <view class="alarm-desc">点击一键报修，我们将安排专业人员上门服务</view>
    </view>
    
    <view class="emergency-services">
      <view class="service-item" @click="callEmergency('110')">
        <view class="service-icon police">
          <uni-icons type="phone-filled" size="24" color="#fff"></uni-icons>
        </view>
        <view class="service-info">
          <view class="service-title">紧急报警</view>
          <view class="service-number">110</view>
        </view>
      </view>
      
      <view class="service-item" @click="callEmergency('119')">
        <view class="service-icon fire">
          <uni-icons type="phone-filled" size="24" color="#fff"></uni-icons>
        </view>
        <view class="service-info">
          <view class="service-title">消防救援</view>
          <view class="service-number">119</view>
        </view>
      </view>
      
      <view class="service-item" @click="callEmergency('120')">
        <view class="service-icon medical">
          <uni-icons type="phone-filled" size="24" color="#fff"></uni-icons>
        </view>
        <view class="service-info">
          <view class="service-title">医疗急救</view>
          <view class="service-number">120</view>
        </view>
      </view>
    </view>
    
    <view class="section guide-section">
      <view class="section-header">
        <view class="section-title">应急指南</view>
        <view class="more-btn" @click="goToGuideList">
          <text>更多</text>
          <uni-icons type="arrowright" size="14" color="#2979ff"></uni-icons>
        </view>
      </view>
      <view class="guide-list">
        <view class="guide-item" v-for="(guide, index) in guides.slice(0, 3)" :key="index" @click="showGuideDrawer(guide)">
          <view class="guide-icon">
            <image :src="guide.icon" mode="aspectFit"></image>
          </view>
          <view class="guide-content">
            <view class="guide-title">{{guide.title}}</view>
            <view class="guide-desc">{{guide.desc}}</view>
          </view>
          <view class="guide-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 抽屉式详情 -->
    <uni-popup ref="popup" type="bottom" @change="drawerChange" :safe-area="false">
      <view class="drawer-content">
        <view class="drawer-header">
          <view class="drawer-title">{{currentGuide.title || '应急指南详情'}}</view>
          <view class="drawer-close" @click="closeDrawer">
            <text class="close-icon">×</text>
          </view>
        </view>
        <scroll-view class="drawer-body" scroll-y>
          <view class="guide-detail">
            <view class="detail-icon" v-if="currentGuide.icon">
              <image :src="currentGuide.icon" mode="aspectFit"></image>
            </view>
            <view class="detail-content" v-html="currentGuide.content || '暂无详细内容'"></view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
    
    <view class="section services-section">
      <view class="section-title">在线服务</view>
      <view class="service-grid">
        <view class="grid-item" @click="contactCustomer">
          <view class="grid-icon">
            <uni-icons type="chat" size="28" color="#2979ff"></uni-icons>
          </view>
          <view class="grid-text">在线咨询</view>
        </view>
        
        <view class="grid-item" @click="reportIssue">
          <view class="grid-icon">
            <uni-icons type="flag" size="28" color="#19be6b"></uni-icons>
          </view>
          <view class="grid-text">隐患上报</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

const popup = ref()
const currentGuide = ref({})

const guides = ref([
        {
          id: 1,
          title: '燃气泄漏应急处理',
          desc: '如何正确处理家庭燃气泄漏情况',
          icon: '/static/images/emergency/gas-leak.png',
          content: `
            <h3>燃气泄漏应急处理步骤</h3>
            <p><strong>1. 立即关闭燃气阀门</strong></p>
            <p>发现燃气泄漏后，首先要保持冷静，立即关闭燃气表前的总阀门或钢瓶角阀。</p>

            <p><strong>2. 开窗通风</strong></p>
            <p>迅速打开门窗，让空气流通，稀释泄漏的燃气浓度。</p>

            <p><strong>3. 严禁明火和电器操作</strong></p>
            <p>不要开关电灯、电器，不要使用打火机、火柴等，避免产生火花引起爆炸。</p>

            <p><strong>4. 疏散人员</strong></p>
            <p>立即疏散现场人员到安全地带，特别是老人和儿童。</p>

            <p><strong>5. 报警求助</strong></p>
            <p>在安全地带拨打燃气公司抢修电话或119报警电话。</p>
          `
        },
        {
          id: 2,
          title: '燃气火灾应对',
          desc: '燃气火灾的应急自救措施',
          icon: '/static/images/emergency/fire.png',
          content: `
            <h3>燃气火灾应急处理</h3>
            <p><strong>1. 立即切断气源</strong></p>
            <p>如果可以安全接近，立即关闭燃气阀门，切断气源。</p>

            <p><strong>2. 使用正确的灭火方法</strong></p>
            <p>使用干粉灭火器、二氧化碳灭火器或沙土灭火，严禁用水扑救燃气火灾。</p>

            <p><strong>3. 疏散逃生</strong></p>
            <p>火势较大时，立即疏散人员，沿安全通道快速撤离。</p>

            <p><strong>4. 报警求助</strong></p>
            <p>立即拨打119火警电话，详细说明火灾地点和燃气泄漏情况。</p>

            <p><strong>5. 等待专业救援</strong></p>
            <p>在安全地带等待消防队员到达，配合救援工作。</p>
          `
        },
        {
          id: 3,
          title: '燃气中毒急救',
          desc: '燃气中毒的症状与应急处理方法',
          icon: '/static/images/emergency/first-aid.png',
          content: `
            <h3>燃气中毒急救处理</h3>
            <p><strong>中毒症状识别：</strong></p>
            <p>头晕、头痛、恶心、呕吐、乏力、意识模糊等。</p>

            <p><strong>1. 迅速脱离现场</strong></p>
            <p>立即将中毒者转移到空气新鲜的地方，解开衣领，保持呼吸道畅通。</p>

            <p><strong>2. 保持呼吸道畅通</strong></p>
            <p>让患者平躺，头部偏向一侧，防止呕吐物堵塞呼吸道。</p>

            <p><strong>3. 人工呼吸</strong></p>
            <p>如患者呼吸停止，立即进行人工呼吸和心肺复苏。</p>

            <p><strong>4. 立即就医</strong></p>
            <p>拨打120急救电话，将患者送往医院救治。</p>

            <p><strong>5. 现场通风</strong></p>
            <p>同时处理泄漏源，开窗通风，防止更多人中毒。</p>
          `
        }
])

// 触发报警
const triggerAlarm = () => {
      uni.showModal({
        title: '确认操作',
        content: '确定要触发紧急报警吗？系统将通知您的紧急联系人并共享您的位置信息',
        success: (res) => {
          if (res.confirm) {
            // 获取位置
            uni.getLocation({
              type: 'gcj02',
              success: (res) => {
                const latitude = res.latitude
                const longitude = res.longitude
                
                // 实际应用中这里应调用API发送报警信息和位置
                uni.showToast({
                  title: '报警信息已发送',
                  icon: 'success'
                })
                
                // 模拟报警后自动拨打紧急电话
                setTimeout(() => {
                  uni.showModal({
                    title: '拨打紧急电话',
                    content: '是否立即拨打紧急电话？',
                    success: (res) => {
                      if (res.confirm) {
                        callEmergency('110')
                      }
                    }
                  })
                }, 1500)
              },
              fail: () => {
                // 无法获取位置时也要发送报警
                uni.showToast({
                  title: '报警信息已发送，但无法获取位置',
                  icon: 'none'
                })
              }
            })
          }
        }
      })
}

// 拨打紧急电话
const callEmergency = (number) => {
      uni.makePhoneCall({
        phoneNumber: number,
        success: () => {
          console.log('拨打电话成功')
        },
        fail: () => {
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          })
        }
      })
}

// 显示抽屉式详情
const showGuideDrawer = (guide) => {
  currentGuide.value = guide
  popup.value.open('bottom')
}

// 关闭抽屉
const closeDrawer = () => {
  popup.value.close()
}

// 抽屉状态改变
const drawerChange = (e) => {
  if (!e.show) {
    currentGuide.value = {}
  }
}

// 跳转到应急指南列表页
const goToGuideList = () => {
  uni.navigateTo({
    url: '/pages/emergency/guide-list'
  })
}
// 在线客服咨询
const contactCustomer = () => {
      uni.showModal({
        title: '联系客服',
        content: '是否拨打客服热线？',
        success: (res) => {
          if (res.confirm) {
            uni.makePhoneCall({
              phoneNumber: '************',
              success: () => {
                console.log('拨打客服电话成功')
              },
              fail: () => {
                uni.showToast({
                  title: '拨打电话失败',
                  icon: 'none'
                })
              }
            })
          }
        }
      })
}

// 一键维修
const callRepair = () => {
      uni.showModal({
        title: '联系维修',
        content: '是否拨打维修服务热线？',
        success: (res) => {
          if (res.confirm) {
            uni.makePhoneCall({
              phoneNumber: '************',
              success: () => {
                console.log('拨打维修电话成功')
              },
              fail: () => {
                uni.showToast({
                  title: '拨打电话失败',
                  icon: 'none'
                })
              }
            })
          }
        }
      })
}

// 隐患上报
const reportIssue = () => {
  uni.navigateTo({
    url: '/pages/service/report'
  })
}
</script>

<style scoped>
.container {
  padding-bottom: 30rpx;
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
}
.emergency-header {
  height: 200rpx;
  background: linear-gradient(to right, #ff5050, #ff7676);
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;
  width: 100%;
  box-sizing: border-box;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.header-desc {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 一键报警 */
.alarm-panel {
  padding: 0 40rpx;
  margin-bottom: 40rpx;
  width: 100%;
  box-sizing: border-box;
}

.sos-btn {
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  background-color: #fa3534;
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 50rpx;
  letter-spacing: 4rpx;
  box-shadow: 0 10rpx 20rpx rgba(250,53,52,0.3);
}

.repair-btn {
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  background-color: #2979ff;
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 50rpx;
  letter-spacing: 4rpx;
  box-shadow: 0 10rpx 20rpx rgba(41,121,255,0.3);
}

.alarm-desc {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
  padding: 0 40rpx;
}

/* 紧急服务 */
.emergency-services {
  display: flex;
  justify-content: space-around;
  padding: 0 20rpx;
  margin-bottom: 40rpx;
}

.service-item {
  display: flex;
  align-items: center;
  padding: 15rpx 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.05);
}

.service-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15rpx;
}

.police {
  background-color: #2979ff;
}

.fire {
  background-color: #fa3534;
}

.medical {
  background-color: #19be6b;
}

.service-title {
  font-size: 24rpx;
  margin-bottom: 5rpx;
}

.service-number {
  font-size: 28rpx;
  font-weight: bold;
}

/* 应急指南 */
.section {
  background-color: #fff;
  border-radius: 12rpx;
  margin: 0 20rpx 30rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.05);
  width: calc(100% - 40rpx);
  box-sizing: border-box;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 10rpx;
  height: 30rpx;
  width: 6rpx;
  background-color: var(--primary-color);
  border-radius: 3rpx;
}

.more-btn {
  display: flex;
  align-items: center;
  color: #2979ff;
  font-size: 28rpx;
}

.more-btn text {
  margin-right: 5rpx;
}

.guide-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.guide-item:last-child {
  border-bottom: none;
}

.guide-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.guide-icon image {
  width: 100%;
  height: 100%;
}

.guide-content {
  flex: 1;
}

.guide-title {
  font-size: 30rpx;
  margin-bottom: 10rpx;
}

.guide-desc {
  font-size: 24rpx;
  color: #999;
}

/* 在线服务 */
.service-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  /* grid-template-rows: 1fr 1fr; */
  grid-gap: 30rpx;
}

.grid-item {
  height: 160rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.grid-icon {
  margin-bottom: 15rpx;
}

.grid-text {
  font-size: 28rpx;
}

/* 抽屉内容样式 */
.drawer-content {
  width: 100%;
  height: 80vh;
  background-color: #fff !important;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

/* 小程序端特殊处理 */
/* #ifdef MP-WEIXIN */
.drawer-content {
  background: #fff !important;
  border-top-left-radius: 30rpx !important;
  border-top-right-radius: 30rpx !important;
}

.drawer-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  z-index: -1;
}
/* #endif */

/* uni-popup 组件样式覆盖 */
:deep(.uni-popup__wrapper-box) {
  border-top-left-radius: 30rpx !important;
  border-top-right-radius: 30rpx !important;
  overflow: hidden;
}

:deep(.uni-popup-bottom) {
  border-top-left-radius: 30rpx !important;
  border-top-right-radius: 30rpx !important;
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.drawer-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.drawer-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.close-icon {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.drawer-close:active {
  transform: scale(0.95);
  background-color: #e5e5e5;
}

.drawer-body {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
  background-color: #fff;
}

.guide-detail {
  line-height: 1.6;
  background-color: #fff;
}

.detail-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 30rpx;
  display: flex;
  justify-content: center;
}

.detail-icon image {
  width: 100%;
  height: 100%;
}

.detail-content {
  font-size: 28rpx;
  color: #333;
}

.detail-content h3 {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #2979ff;
}

.detail-content p {
  margin-bottom: 15rpx;
  line-height: 1.8;
}

.detail-content strong {
  color: #fa3534;
  font-weight: bold;
}
</style>