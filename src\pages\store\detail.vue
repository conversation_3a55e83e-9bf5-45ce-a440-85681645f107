<template>
  <view class="container" :class="{'page-locked': showAlarmListDrawer || showCylinderListDrawer}">
    <!-- 顶部区域 -->
    <view class="header-section" :style="headerStyle">
      <!-- 背景图片 -->
      <image
        v-if="storeDetail.backgroundImage"
        class="store-banner"
        :src="storeDetail.backgroundImage"
        mode="aspectFill"
      ></image>

      <!-- 上传背景图按钮 -->
      <view class="upload-bg-btn" @click="uploadBackgroundImage">
        <text class="upload-icon">📷</text>
        <text class="upload-text">更换背景</text>
      </view>

      <view class="header-content">
        <view class="store-name-container">
          <text class="store-name">{{ storeDetail.name }}</text>
          <text v-if="storeDetail.isMain" class="main-tag">总店</text>
        </view>
        <text class="store-address">{{ storeDetail.address }}</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 主要内容区域 -->
    <view v-else class="content-section">
      <!-- 快速信息卡片 -->
      <view class="quick-info-card">
        <view class="quick-info-item" @click="showAlarmDrawer">
          <text class="quick-info-value">{{
            storeDetail.alarmCount || 0
          }}</text>
          <text class="quick-info-label">报警器</text>
        </view>
        <view class="divider"></view>
        <view class="quick-info-item" @click="showCylinderDrawer">
          <text class="quick-info-value">{{
            storeDetail.cylinderCount || 0
          }}</text>
          <text class="quick-info-label">气瓶</text>
        </view>
      </view>

      <!-- 基本信息 -->
      <view class="detail-section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>基本信息</text>
        </view>

        <view class="info-card">
          <view class="info-row">
            <text class="info-label">用气户名</text>
            <text class="info-value">{{ storeDetail.name }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">用气类型</text>
            <text class="info-value">{{ storeDetail.gasType || "暂无" }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">用气地址</text>
            <text class="info-value">{{ storeDetail.address }}</text>
          </view>
          <view class="info-row" v-if="storeDetail.detailAddress">
            <text class="info-label">详细地址</text>
            <text class="info-value">{{ storeDetail.detailAddress }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">联系电话</text>
            <text class="info-value">{{ storeDetail.phone || "暂无" }}</text>
          </view>

          <!-- 身份证照片 -->
          <view class="info-row image-row">
            <text class="info-label id-card-label">身份证照片（正面）</text>
            <view class="info-value">
              <image
                v-if="storeDetail.idCardFront"
                class="id-card-image"
                :src="storeDetail.idCardFront"
                mode="aspectFit"
                @click="previewImage(storeDetail.idCardFront)"
              ></image>
              <text v-else class="no-image-text">暂无</text>
            </view>
          </view>

          <view class="info-row image-row">
            <text class="info-label id-card-label">身份证照片（反面）</text>
            <view class="info-value">
              <image
                v-if="storeDetail.idCardBack"
                class="id-card-image"
                :src="storeDetail.idCardBack"
                mode="aspectFit"
                @click="previewImage(storeDetail.idCardBack)"
              ></image>
              <text v-else class="no-image-text">暂无</text>
            </view>
          </view>

          <view class="info-row image-row">
            <text class="info-label">营业执照图片</text>
            <view class="info-value">
              <image
                v-if="storeDetail.businessLicense"
                class="license-image"
                :src="storeDetail.businessLicense"
                mode="aspectFit"
                @click="previewImage(storeDetail.businessLicense)"
              ></image>
              <text v-else class="no-image-text">暂无</text>
            </view>
          </view>

          <view class="info-row" v-if="storeDetail.licenseValidPeriod">
            <text class="info-label">营业执照有效期</text>
            <text class="info-value">{{ storeDetail.licenseValidPeriod }}</text>
          </view>
          <view class="info-row" v-if="storeDetail.businessHours">
            <text class="info-label">营业时间</text>
            <text class="info-value">{{ storeDetail.businessHours }}</text>
          </view>
          <view class="info-row" v-if="storeDetail.dailyCloseTime">
            <text class="info-label">每日关阀时间</text>
            <text class="info-value">{{ storeDetail.dailyCloseTime }}</text>
          </view>

          <view class="info-row" v-if="storeDetail.description">
            <text class="info-label">备注信息</text>
            <text class="info-value description">{{ storeDetail.description }}</text>
          </view>
        </view>
      </view>

      <!-- 合规信息 -->
      <view class="compliance-section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>合规信息</text>
        </view>

        <view class="compliance-card">
          <view class="compliance-item">
            <view class="compliance-label-container">
              <text class="compliance-label">合同状态</text>
            </view>
            <view
              :class="[
                'compliance-status',
                `status-bg-${storeDetail.contractStatus}`,
              ]"
            >
              {{ getStatusText(storeDetail.contractStatus) }}
            </view>
          </view>

          <view class="compliance-item">
            <view class="compliance-label-container">
              <text class="compliance-label">设备安装</text>
            </view>
            <view
              :class="[
                'compliance-status',
                `status-bg-${storeDetail.equipmentStatus}`,
              ]"
            >
              {{ getEquipmentStatusText(storeDetail.equipmentStatus) }}
            </view>
          </view>
          <view class="compliance-item">
            <view class="compliance-label-container">
              <text class="compliance-label">安全指导</text>
            </view>
            <!-- 只在不是未预约状态时显示状态标签 -->
            <view
              v-if="storeDetail.safetyGuidanceStatus && storeDetail.safetyGuidanceStatus !== 'not_scheduled'"
              :class="[
                'compliance-status',
                getGuidanceStatusClass(storeDetail.safetyGuidanceStatus),
              ]"
            >
              {{ getSafetyGuidanceStatusText(storeDetail.safetyGuidanceStatus) }}
            </view>
            <!-- 预约按钮 -->
            <view
              class="compliance-status status-bg-processing"
              v-if="canScheduleSafetyGuidance"
              @click="handleScheduleSafetyAdvisor"
            >
              去预约
            </view>
          </view>
          <view class="compliance-item">
            <view class="compliance-label-container">
              <text class="compliance-label">安全审查</text>
            </view>
            <view class="compliance-status status-bg-check" @click="viewSafetyCheck">
              点击查看
            </view>
          </view>
        </view>
      </view>

      <!-- 安全指导预约信息 -->
      <view class="guidance-section" v-if="storeDetail.safetyGuidanceStatus === 'scheduled'">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>安全指导预约信息</text>
        </view>

        <view class="guidance-card">
          <view class="guidance-time">
            <view class="guidance-date">
              <uni-icons type="calendar" color="#4c6ef5" size="20"></uni-icons>
              <text>{{ storeDetail.guidanceDate || '未知' }}</text>
            </view>
            <view class="guidance-hour">
              <uni-icons type="clock" color="#4c6ef5" size="20"></uni-icons>
              <text>{{ storeDetail.guidanceTimeSlot || '未知时段' }}</text>
            </view>
          </view>
          
          <view class="guidance-info">
            <view class="guidance-info-item">
              <text class="guidance-label">联系人</text>
              <text class="guidance-value">{{ storeDetail.guidanceContact || '未知' }}</text>
            </view>
            <view class="guidance-info-item">
              <text class="guidance-label">联系电话</text>
              <text class="guidance-value">{{ storeDetail.guidancePhone || '未知' }}</text>
            </view>
            <view class="guidance-info-item" v-if="storeDetail.guidanceRemarks">
              <text class="guidance-label">备注信息</text>
              <text class="guidance-value">{{ storeDetail.guidanceRemarks }}</text>
            </view>
            <view class="cancel-btn-container">
              <button class="cancel-guidance-btn" @click="cancelGuidance">
                <text>取消预约</text>
              </button>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 地图位置 -->
      <view class="map-section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>店铺位置</text>
        </view>

        <view class="map-card">
          <view v-if="storeDetail.latitude && storeDetail.longitude">
            <map
              class="map"
              :latitude="storeDetail.latitude"
              :longitude="storeDetail.longitude"
              :markers="markers"
              scale="16"
            ></map>
            <!-- <view class="coordinate-container">
              <view class="coordinate-item">
                <text class="coordinate-label">经度</text>
                <text class="coordinate-value">{{
                  storeDetail.longitude
                }}</text>
              </view>
              <view class="coordinate-item">
                <text class="coordinate-label">纬度</text>
                <text class="coordinate-value">{{ storeDetail.latitude }}</text>
              </view>
            </view> -->
          </view>
          <view v-else class="no-location">
            <text class="no-data-text">暂无位置信息</text>
          </view>
        </view>
      </view>

      <!-- 底部区域 -->
      <!-- <view class="footer-section">
        <button class="action-button edit" @click="editStore">编辑店铺</button>
        <button class="action-button manage" @click="manageStore">
          管理店铺
        </button>
      </view> -->
    </view>
    
    <!-- 报警器列表抽屉 -->
    <view class="device-drawer" :class="{'drawer-show': showAlarmListDrawer}" @touchmove.prevent>
      <view class="drawer-mask" @click="closeAlarmDrawer"></view>
      <view class="drawer-content">
        <view class="drawer-header">
          <text class="drawer-title">报警器列表 ({{alarmList.length}})</text>
          <view class="drawer-close" @click="closeAlarmDrawer">
            <text class="close-icon">×</text>
          </view>
        </view>
        
        <scroll-view scroll-y class="drawer-body" @touchmove.stop>
          <view class="device-list">
            <view class="device-item" v-for="(alarm, index) in alarmList" :key="index">
              <view class="device-header">
                <view class="device-name">{{ alarm.code }}</view>
                <view class="device-status" :class="{'status-online': alarm.isOnline, 'status-offline': !alarm.isOnline}">
                  <view class="status-dot"></view>
                  {{ alarm.isOnline ? '在线' : '离线' }}
                </view>
              </view>
              
              <view class="device-info">
                <view class="info-row">
                  <text class="info-label">报警器编号</text>
                  <text class="info-value">{{ alarm.code }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">状态</text>
                  <text class="info-value" :class="{'text-success': alarm.status === 'normal', 'text-error': alarm.status === 'warning'}">
                    {{ alarm.status === 'normal' ? '正常' : '异常' }}
                  </text>
                </view>
                <view class="info-row">
                  <text class="info-label">电量</text>
                  <view class="info-value">
                    <view class="battery-bar">
                      <view class="battery-fill" :style="{width: alarm.battery + '%', backgroundColor: getBatteryColor(alarm.battery)}"></view>
                    </view>
                    <text class="battery-text" :class="{'battery-low': alarm.battery < 20}" :style="{color: getBatteryTextColor(alarm.battery)}">{{ alarm.battery }}%</text>
                  </view>
                </view>
              </view>
            </view>
            
            <view v-if="alarmList.length === 0" class="empty-list">
              <text class="empty-text">暂无报警器设备</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
    
    <!-- 气瓶列表抽屉 -->
    <view class="device-drawer" :class="{'drawer-show': showCylinderListDrawer}" @touchmove.prevent>
      <view class="drawer-mask" @click="closeCylinderDrawer"></view>
      <view class="drawer-content">
        <view class="drawer-header">
          <text class="drawer-title">气瓶列表 ({{cylinderList.length}})</text>
          <view class="drawer-close" @click="closeCylinderDrawer">
            <text class="close-icon">×</text>
          </view>
        </view>
        
        <scroll-view scroll-y class="drawer-body" @touchmove.stop>
          <view class="device-list">
            <view class="device-item" v-for="(cylinder, index) in cylinderList" :key="index">
              <view class="device-header">
                <view class="device-name">{{ cylinder.code }}</view>
                <view class="device-status" :class="{'status-online': cylinder.isOnline, 'status-offline': !cylinder.isOnline}">
                  <view class="status-dot"></view>
                  {{ cylinder.isOnline ? '在线' : '离线' }}
                </view>
              </view>
              
              <view class="device-info">
                <view class="info-row">
                  <text class="info-label">气瓶编码</text>
                  <text class="info-value">{{ cylinder.code }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">阀门状态</text>
                  <text class="info-value" :class="{'text-primary': cylinder.valveOpen, 'text-muted': !cylinder.valveOpen}">
                    {{ cylinder.valveOpen ? '开启' : '关闭' }}
                  </text>
                </view>
                <view class="info-row">
                  <text class="info-label">电量</text>
                  <view class="info-value">
                    <view class="battery-bar">
                      <view class="battery-fill" :style="{width: cylinder.battery + '%', backgroundColor: getBatteryColor(cylinder.battery)}"></view>
                    </view>
                    <text class="battery-text" :class="{'battery-low': cylinder.battery < 20}" :style="{color: getBatteryTextColor(cylinder.battery)}">{{ cylinder.battery }}%</text>
                  </view>
                </view>
                <view class="info-row">
                  <text class="info-label">充装重量</text>
                  <text class="info-value">{{ cylinder.fillingWeight }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">开始使用时间</text>
                  <text class="info-value">{{ cylinder.fillingDate }}</text>
                </view>
              </view>
            </view>
            
            <view v-if="cylinderList.length === 0" class="empty-list">
              <text class="empty-text">暂无气瓶设备</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const storeId = ref(null)
const storeDetail = ref({})
const loading = ref(true)
const markers = ref([])
const showAlarmListDrawer = ref(false)
const showCylinderListDrawer = ref(false)
const alarmList = ref([])
const cylinderList = ref([])

// 计算属性
// 判断是否可以预约安全指导
const canScheduleSafetyGuidance = computed(() => {
  if (!storeDetail.value || typeof storeDetail.value !== 'object') {
    return false;
  }
  return storeDetail.value.safetyGuidanceStatus === 'not_scheduled' ||
         storeDetail.value.safetyGuidanceStatus === 'failed' ||
         !storeDetail.value.safetyGuidanceStatus;
})

// 头部样式
const headerStyle = computed(() => {
  if (storeDetail.value?.backgroundImage) {
    return {};
  }
  return {
    background: 'linear-gradient(135deg, #4a66b7 0%, #2979ff 100%)'
  };
})

// 店铺是否有效
const isValidStore = computed(() => {
  return storeDetail.value && Object.keys(storeDetail.value).length > 0;
})

// 页面加载时的处理
const onLoad = (options) => {
  if (options.id) {
    storeId.value = options.id;
    loadStoreDetail();
  } else {
    uni.showToast({
      title: "参数错误",
      icon: "none",
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
}

// 组件挂载时自动加载数据
onMounted(() => {
  // 从页面参数获取storeId，如果没有则使用默认值进行测试
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || {};

  if (options.id) {
    storeId.value = options.id;
  } else {
    // 如果没有传入id，使用默认值1进行测试
    storeId.value = '1';
  }

  loadStoreDetail();
})

// 加载店铺详情
const loadStoreDetail = async () => {
  try {
    loading.value = true;

    // 模拟接口请求
    await new Promise(resolve => setTimeout(resolve, 300)); // 减少加载时间

    // 模拟数据，实际项目中应该从后端获取
    if (storeId.value == 1) {
      storeDetail.value = {
            id: 1,
            name: "燃气经营部(总店)",
            address: "广州市天河区珠江新城88号",
            detailAddress: "A座15楼1501室",
            gasType: "商业用气",
            image:
              "https://ts3.tc.mm.bing.net/th/id/OIP-C.P-mROslgWTHtZEoWdbNzCQHaFj?rs=1&pid=ImgDetMain&o=7&rm=3",
            backgroundImage: "",
            idCardFront: "https://via.placeholder.com/300x200/4a66b7/ffffff?text=身份证正面",
            idCardBack: "https://via.placeholder.com/300x200/2979ff/ffffff?text=身份证反面",
            businessLicense: "https://via.placeholder.com/300x200/34c759/ffffff?text=营业执照",
            licenseValidPeriod: "2020-01-01 ~ 2030-12-31",
            dailyCloseTime: "22:30",
            contractStatus: "passed",
            alarmCount: 12,
            cylinderCount: 50,
            insuranceStatus: "passed",
            equipmentStatus: "installed", // 'installed', 'not_installed'
            safetyStatus: "processing",
            safetyGuidanceStatus: "passed", // 'not_scheduled', 'scheduled', 'passed', 'failed'
            isMain: true,
            phone: "020-88888888",
            businessHours: "09:00-21:00",
            description:
              "本店专营各类液化石油气钢瓶销售、配送服务，提供安全可靠的燃气产品。",
            latitude: 23.129163,
            longitude: 113.264435,
          };
          
          // 模拟报警器数据
          alarmList.value = [
            {
              id: 101,
              name: "厨房报警器",
              code: "ALARM-001",
              status: "normal",
              isOnline: true,
              battery: 85,
              location: "厨房"
            },
            {
              id: 102,
              name: "客厅报警器",
              code: "ALARM-002",
              status: "normal",
              isOnline: true,
              battery: 92,
              location: "客厅"
            },
            {
              id: 103,
              name: "卧室报警器",
              code: "ALARM-003",
              status: "warning",
              isOnline: false,
              battery: 15,
              location: "卧室"
            }
          ];
          
          // 模拟气瓶数据
          cylinderList.value = [
            {
              id: 201,
              name: "厨房主气瓶",
              code: "LPG12345678",
              isOnline: true,
              valveOpen: true,
              battery: 85,
              fillingCompany: "广州燃气集团",
              fillingDate: "2023-09-15",
              fillingWeight: "15kg"
            },
            {
              id: 202,
              name: "备用气瓶",
              code: "LPG87654321",
              isOnline: true,
              valveOpen: false,
              battery: 32,
              fillingCompany: "广州燃气集团",
              fillingDate: "2023-11-20",
              fillingWeight: "15kg"
            }
          ];
        } else if (this.storeId == 2) {
          this.storeDetail = {
            id: 2,
            name: "燃气经营部(分店)",
            address: "广州市海珠区滨江东路120号",
            detailAddress: "B栋3楼302号",
            gasType: "居民用气",
            image:
              "https://ts3.tc.mm.bing.net/th/id/OIP-C.P-mROslgWTHtZEoWdbNzCQHaFj?rs=1&pid=ImgDetMain&o=7&rm=3",
            backgroundImage: "",
            idCardFront: "https://via.placeholder.com/300x200/ff9500/ffffff?text=身份证正面",
            idCardBack: "https://via.placeholder.com/300x200/ff3b30/ffffff?text=身份证反面",
            businessLicense: "https://via.placeholder.com/300x200/909399/ffffff?text=营业执照",
            licenseValidPeriod: "2021-06-15 ~ 2031-06-14",
            dailyCloseTime: "23:00",
            contractStatus: "processing",
            alarmCount: 5,
            cylinderCount: 23,
            insuranceStatus: "pending",
            equipmentStatus: "not_installed", // 'installed', 'not_installed'
            safetyStatus: "pending",
            safetyGuidanceStatus: "scheduled",
            guidanceDate: "2023-10-25",
            guidanceTimeSlot: "下午 13:00-15:00",
            guidanceContact: "张经理",
            guidancePhone: "***********",
            guidanceRemarks: "请提前准备好相关证件和材料",
            isMain: false,
            phone: "020-66666666",
            businessHours: "08:30-20:30",
            description: "分店主营液化石油气零售服务，提供优质的客户体验。",
            latitude: 23.099994,
            longitude: 113.344818,
          };
          
          // 模拟报警器数据
          alarmList.value = [
            {
              id: 104,
              name: "前台报警器",
              code: "ALARM-004",
              status: "normal",
              isOnline: true,
              battery: 76,
              location: "前台"
            },
            {
              id: 105,
              name: "仓库报警器",
              code: "ALARM-005",
              status: "normal",
              isOnline: true,
              battery: 68,
              location: "仓库"
            }
          ];
          
          // 模拟气瓶数据
          cylinderList.value = [
            {
              id: 203,
              name: "前台气瓶",
              code: "LPG11112222",
              isOnline: true,
              valveOpen: true,
              battery: 78,
              fillingCompany: "广州燃气集团",
              fillingDate: "2023-08-10",
              fillingWeight: "5kg"
            },
            {
              id: 204,
              name: "库存气瓶-1",
              code: "LPG33334444",
              isOnline: true,
              valveOpen: false,
              battery: 45,
              fillingCompany: "广州燃气集团",
              fillingDate: "2023-08-10",
              fillingWeight: "15kg"
            },
            {
              id: 205,
              name: "库存气瓶-2",
              code: "LPG55556666",
              isOnline: false,
              valveOpen: false,
              battery: 13,
              fillingCompany: "广州燃气集团",
              fillingDate: "2023-08-10",
              fillingWeight: "15kg"
            }
          ];
        } else {
          uni.showToast({
            title: "店铺不存在",
            icon: "none",
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
          return;
        }

        // 设置地图标记点
        if (storeDetail.value.latitude && storeDetail.value.longitude) {
          markers.value = [
            {
              id: 1,
              latitude: storeDetail.value.latitude,
              longitude: storeDetail.value.longitude,
              title: storeDetail.value.name,
              iconPath: "/static/images/marker.png",
              width: 32,
              height: 32,
            },
          ];
        }

    loading.value = false;
  } catch (error) {
    console.error('加载店铺详情失败:', error);
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
    loading.value = false;
  }
}

// 抽屉控制方法
const showAlarmDrawer = () => {
  showAlarmListDrawer.value = true;
}

const closeAlarmDrawer = () => {
  showAlarmListDrawer.value = false;
}

const showCylinderDrawer = () => {
  showCylinderListDrawer.value = true;
}

const closeCylinderDrawer = () => {
  showCylinderListDrawer.value = false;
}
// 根据电量获取颜色
const getBatteryColor = (battery) => {
  if (battery >= 60) return '#4CD964'; // 绿色
  if (battery >= 30) return '#FF9500'; // 橙色
  return '#FF3B30'; // 红色
}

// 根据电量获取文字颜色
const getBatteryTextColor = (battery) => {
  if (battery >= 60) return '#4CD964'; // 绿色
  if (battery >= 30) return '#FF9500'; // 橙色
  return '#FF3B30'; // 红色
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: "未提交",
    processing: "审核中",
    passed: "已通过",
    failed: "未通过",
  };
  return statusMap[status] || "未提交";
}

// 获取设备安装状态文本
const getEquipmentStatusText = (status) => {
  const statusMap = {
    installed: "已安装",
    not_installed: "未安装",
  };
  return statusMap[status] || "未知";
}

// 获取安全指导状态文本
const getSafetyGuidanceStatusText = (status) => {
  const statusMap = {
    not_scheduled: "未预约",
    scheduled: "已预约",
    passed: "已通过",
    failed: "未通过",
  };
  return statusMap[status] || "未预约";
}
// 获取安全指导状态对应的样式类
const getGuidanceStatusClass = (status) => {
  // 映射安全指导状态到对应的样式类
  const classMap = {
    not_scheduled: "status-bg-pending",
    scheduled: "status-bg-processing",
    passed: "status-bg-passed",
    failed: "status-bg-failed"
  };
  return classMap[status] || "status-bg-pending";
}

// 处理预约安全指导员
const handleScheduleSafetyAdvisor = () => {
  uni.navigateTo({
    url: `/pages/store/add-store?id=${storeId.value}`
  });
}

// 取消预约
const cancelGuidance = async () => {
  try {
    const res = await uni.showModal({
      title: '提示',
      content: '确定要取消预约吗？'
    });

    if (res.confirm) {
      uni.showLoading({
        title: '取消中'
      });

      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 800));

      // 更新状态
      storeDetail.value.safetyGuidanceStatus = 'not_scheduled';
      delete storeDetail.value.guidanceDate;
      delete storeDetail.value.guidanceTimeSlot;
      delete storeDetail.value.guidanceContact;
      delete storeDetail.value.guidancePhone;
      delete storeDetail.value.guidanceRemarks;

      uni.hideLoading();
      uni.showToast({
        title: '已取消预约',
        icon: 'success'
      });
    }
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: '取消失败',
      icon: 'none'
    });
  }
}

// 编辑租户 (当前被注释，如需使用请取消注释模板中的按钮)
const editStore = () => {
  uni.navigateTo({
    url: `/pages/store/edit?id=${storeId.value}`,
  });
}

// 管理租户 (当前被注释，如需使用请取消注释模板中的按钮)
const manageStore = () => {
  uni.setStorageSync("currentStoreId", storeId.value);
  uni.navigateTo({
    url: `/pages/store/manage?id=${storeId.value}`,
  });
}

// 上传背景图
const uploadBackgroundImage = async () => {
  try {
    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera']
    });

    const tempFilePath = res.tempFilePaths[0];

    uni.showLoading({
      title: '上传中...'
    });

    // 模拟上传过程
    await new Promise(resolve => setTimeout(resolve, 1000));

    storeDetail.value.backgroundImage = tempFilePath;
    uni.hideLoading();
    uni.showToast({
      title: '背景图更换成功',
      icon: 'success'
    });
  } catch (error) {
    uni.hideLoading();
    console.error('上传背景图失败:', error);
    uni.showToast({
      title: '上传失败',
      icon: 'none'
    });
  }
}

// 预览图片
const previewImage = (imageUrl) => {
  uni.previewImage({
    urls: [imageUrl],
    current: imageUrl
  });
}

// 查看安全审查详情
const viewSafetyCheck = () => {
  uni.navigateTo({
    url: `/pages/store/safety-check?id=${storeId.value}`
  });
}

// 导出页面生命周期方法供uni-app使用
defineExpose({
  onLoad
})
</script>

<style scoped>
page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  overflow-x: hidden;
}

.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
}

/* 顶部区域 */
.header-section {
  position: relative;
  height: 400rpx;
  background: linear-gradient(135deg, #4a66b7 0%, #2979ff 100%);
}

.back-btn {
  position: absolute;
  top: 40rpx;
  left: 30rpx;
  width: 70rpx;
  height: 70rpx;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.icon-back {
  font-size: 38rpx;
  color: #333;
}

.store-banner {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border-bottom-left-radius: 30rpx;
  border-bottom-right-radius: 30rpx;
  overflow: hidden;
  display: block;
  z-index: 1;
}

.upload-bg-btn {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 30rpx;
  padding: 12rpx 20rpx;
  display: flex;
  align-items: center;
  z-index: 10;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.upload-bg-btn:active {
  transform: scale(0.95);
  background-color: rgba(0, 0, 0, 0.7);
}

.upload-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #fff;
}

.header-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 60rpx 30rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  color: #fff;
  z-index: 2;
}

.store-name-container {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.store-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-right: 16rpx;
}

.main-tag {
  background-color: #ff9500;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}

.store-address {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(0, 0, 0, 0.1);
  border-top-color: #2979ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 内容区域 */
.content-section {
  flex: 1;
  padding: 0 30rpx 50rpx;
  margin-top: -40rpx;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 快速信息卡片 */
.quick-info-card {
  position: relative;
  z-index: 2;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.quick-info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 20rpx 0;
  transition: all 0.2s ease;
  border-radius: 8rpx;
}

.quick-info-item:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.divider {
  width: 2rpx;
  height: 50rpx;
  background-color: #eee;
}

.quick-info-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.quick-info-label {
  font-size: 24rpx;
  color: #666;
}

.status-indicator {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  margin-bottom: 8rpx;
}

.status-pending {
  background-color: #909399;
}

.status-processing {
  background-color: #ff9500;
}

.status-passed {
  background-color: #34c759;
}

.status-failed {
  background-color: #ff3b30;
}

/* 各部分标题 */
.section-title {
  display: flex;
  align-items: center;
  margin: 30rpx 0 20rpx;
}

.section-title-bar {
  width: 6rpx;
  height: 32rpx;
  background-color: #2979ff;
  margin-right: 16rpx;
  border-radius: 3rpx;
}

.section-title text {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

/* 详细信息卡片 */
.info-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.info-row {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  width: 220rpx;
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
}

/* 身份证标签特殊样式 - 适应手机屏幕 */
.id-card-label {
  width: 200rpx !important;
  font-size: 24rpx;
  line-height: 1.3;
  word-break: break-all;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  justify-content: end;
  word-break: break-all;
  overflow-wrap: break-word;
  max-width: calc(100% - 240rpx);
}

.info-value.description {
  line-height: 1.6;
}

/* 图片行样式 */
.image-row {
  align-items: flex-start;
  padding: 24rpx 0;
}

.image-row .info-value {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.id-card-image {
  width: 160rpx;
  height: 100rpx;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
}

.license-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 8rpx;
  border: 1rpx solid #e0e0e0;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
}

.id-card-image:active,
.license-image:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.no-image-text {
  color: #999;
  font-size: 26rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  border: 1rpx dashed #ddd;
  text-align: center;
  min-width: 160rpx;
}



/* 合规信息 */
.compliance-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.compliance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.compliance-item:last-child {
  border-bottom: none;
}

.compliance-label-container {
  display: flex;
  align-items: center;
}

.compliance-label {
  font-size: 28rpx;
  color: #333;
}

.compliance-status {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.status-bg-pending {
  background-color: #f2f3f5;
  color: #909399;
}

.status-bg-processing {
  background-color: #fff8e6;
  color: #ff9500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.status-bg-processing:active {
  transform: scale(0.95);
  background-color: #fff0d6;
}

.status-bg-passed {
  background-color: #ecf8f0;
  color: #34c759;
}

.status-bg-failed {
  background-color: #fee;
  color: #ff3b30;
}

/* 设备安装状态样式 */
.status-bg-installed {
  background-color: #ecf8f0;
  color: #34c759;
}

.status-bg-not_installed {
  background-color: #f2f3f5;
  color: #909399;
}

/* 安全审查点击查看样式 */
.status-bg-check {
  background-color: #e8f0ff;
  color: #4a66b7;
  cursor: pointer;
  transition: all 0.3s ease;
}

.status-bg-check:active {
  transform: scale(0.95);
  background-color: #d6e4ff;
}

/* 合规信息更新 */
.compliance-right {
  display: flex;
  align-items: center;
}

.compliance-status {
  display: flex;
  align-items: center;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.status-icon {
  margin-right: 6rpx;
  display: flex;
  align-items: center;
}

/* 地图部分 */
.map-card {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.map {
  width: 100%;
  height: 360rpx;
}

.coordinate-container {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
}

.coordinate-item {
  display: flex;
  flex-direction: column;
}

.coordinate-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.coordinate-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.no-location {
  height: 200rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-data-text {
  font-size: 28rpx;
  color: #999;
}

/* 预约按钮样式 */
.schedule-btn {
  margin-right: 0;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #fff;
  background: linear-gradient(135deg, #ff9f43 0%, #ff7f00 100%);
  border-radius: 30rpx;
  padding: 8rpx 20rpx;
  line-height: 1.5;
  margin-left: 16rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 159, 67, 0.3);
}

.schedule-btn text {
  margin-left: 6rpx;
}

.schedule-btn:after {
  border: none;
}

/* 安全指导预约相关样式 */
.guidance-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
}

.guidance-time {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
  margin-bottom: 20rpx;
}

.guidance-date, .guidance-hour {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.guidance-date text, .guidance-hour text {
  margin-left: 10rpx;
}

.guidance-info {
  display: flex;
  flex-direction: column;
}

.guidance-info-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.guidance-info-item:last-child {
  border-bottom: none;
}

.guidance-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.guidance-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

.guidance-status {
  display: none; /* Hide the original guidance-status section */
}

.guidance-status-badge {
  display: none; /* Hide this element completely */
}

.cancel-btn-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
}

.cancel-guidance-btn {
  display: inline-block;
  font-size: 24rpx;
  color: #ff3b30;
  background-color: transparent;
  border: 1rpx solid #ff3b30;
  border-radius: 30rpx;
  padding: 6rpx 20rpx;
  line-height: 1.5;
}

.cancel-guidance-btn:after {
  border: none;
}

/* 底部区域 */
.footer-section {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  box-sizing: border-box;
}

.action-button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin: 0 10rpx;
}

.action-button::after {
  border: none;
}

.action-button.edit {
  background-color: #fff;
  color: #2979ff;
  border: 1rpx solid #2979ff;
}

.action-button.manage {
  background-color: #2979ff;
  color: #fff;
}

/* 适配小屏幕 */
@media screen and (max-width: 375px) {
  .header-section {
    height: 350rpx;
  }

  .store-name {
    font-size: 32rpx;
  }

  .info-label,
  .info-value,
  .compliance-label {
    font-size: 26rpx;
  }

  .map {
    height: 320rpx;
  }
}

/* 页面锁定 */
.page-locked {
  overflow: hidden;
  height: 100vh;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

/* 设备列表抽屉样式 - 优化版 */
.device-drawer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  pointer-events: none;
}

.drawer-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  backdrop-filter: blur(2px);
  pointer-events: none;
}

.drawer-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 70vh;
  background-color: #fff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  box-shadow: 0 -8rpx 30rpx rgba(0, 0, 0, 0.1);
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  display: flex;
  flex-direction: column;
  pointer-events: none;
}

.drawer-show {
  pointer-events: auto;
}

.drawer-show .drawer-mask {
  opacity: 1;
  pointer-events: auto;
}

.drawer-show .drawer-content {
  transform: translateY(0);
  pointer-events: auto;
}

.drawer-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.drawer-header::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 16rpx;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #e0e0e0;
  border-radius: 3rpx;
}

.drawer-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-top: 10rpx;
}

.drawer-close {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.drawer-close:active {
  background-color: #e0e0e0;
  transform: scale(0.95);
}

.close-icon {
  font-size: 36rpx;
  font-weight: bold;
  color: #666;
  line-height: 1;
}

.drawer-body {
  flex: 1;
  padding: 20rpx 0 30rpx 30rpx;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  width: initial !important;
}

/* 设备列表样式 - 优化版 */
.device-list {
  padding-right: 30rpx;
  display: flex;
  flex-direction: column;
}

.device-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 15rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
}

.device-item:active {
  transform: scale(0.99);
  box-shadow: 0 1rpx 8rpx rgba(0, 0, 0, 0.05);
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.device-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.device-status {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
}

.status-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.status-online {
  background-color: rgba(25, 190, 107, 0.1);
  color: #19be6b;
  border: 1rpx solid rgba(25, 190, 107, 0.2);
}

.status-online .status-dot {
  background-color: #19be6b;
  box-shadow: 0 0 5rpx rgba(25, 190, 107, 0.5);
}

.status-offline {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
  border: 1rpx solid rgba(144, 147, 153, 0.2);
}

.status-offline .status-dot {
  background-color: #909399;
}

.device-info {
  display: flex;
  flex-direction: column;
}

.info-row {
  display: flex;
  justify-content: space-between;
  padding: 14rpx 0;
  border-bottom: 1rpx solid #f9f9f9;
}

.info-row:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  color: #888;
  font-size: 28rpx;
}

.info-value {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
}

.text-success {
  color: #19be6b;
}

.text-error {
  color: #fa3534;
}

.text-primary {
  color: #2979ff;
}

.text-muted {
  color: #909399;
}

/* 电量条样式 - 优化版 */
.battery-bar {
  width: 100rpx;
  height: 14rpx;
  background-color: #f0f0f0;
  border-radius: 7rpx;
  overflow: hidden;
  display: inline-block;
  vertical-align: middle;
  margin-right: 10rpx;
  box-shadow: inset 0 1rpx 3rpx rgba(0,0,0,0.1);
}

.battery-fill {
  height: 100%;
  border-radius: 7rpx;
  transition: all 0.3s ease;
}

.battery-text {
  font-size: 24rpx;
  font-weight: 500;
  vertical-align: middle;
}

/* 电量低警告动画 */
@keyframes batteryLowPulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

.battery-low {
  animation: batteryLowPulse 2s infinite;
}

/* 空列表状态 - 优化版 */
.empty-list {
  padding: 80rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}
</style>
