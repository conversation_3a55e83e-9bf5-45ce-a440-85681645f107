# 订单详情页面新功能使用指南

## 🎯 功能概述

本次更新为订单详情页面添加了两个重要功能：

1. **安全环境检查** - 维保员到达现场后的安全环境合格检查步骤
2. **工作人员电话拨打** - 点击配送员/维保员姓名直接拨打电话

## 🚀 快速体验

### 查看页面
访问订单详情页面：`src/pages/order/detail.vue`

### 演示功能
页面右上角有两个演示按钮：
- **切换安全检查状态演示** - 查看合格/不合格状态效果
- **测试拨打电话功能** - 测试电话拨打功能

## 📱 功能详情

### 1. 安全环境检查

**位置**: 维保员任务进度 → "开始作业"步骤之后

**合格状态显示**:
```
✅ 安全环境合格
现场照片：[照片1] [照片2]
```

**不合格状态显示**:
```
⚠️ 安全环境不合格
发现问题：
1. 燃气管道老化，存在漏气风险
2. 通风不良，空气流通不畅
3. 周围有易燃物品堆放
现场照片：[照片1] [照片2]
```

**交互功能**:
- 点击照片可以预览放大
- 点击演示按钮可以切换状态

### 2. 工作人员电话拨打

**适用范围**: 所有配送员和维保员任务

**显示效果**:
- 配送员：王师傅 📞 (蓝色可点击)
- 维保员：李师傅 📞 (蓝色可点击)

**交互流程**:
1. 点击工作人员姓名
2. 弹出确认对话框
3. 确认后调用系统拨号功能

## 🛠️ 技术实现

### 数据结构

**安全检查数据**:
```javascript
safetyCheck: {
  status: 'qualified', // 'qualified' | 'unqualified'
  issues: ['问题描述1', '问题描述2'],
  images: ['图片路径1', '图片路径2']
}
```

**工作人员数据**:
```javascript
{
  worker: '工作人员姓名',
  workerPhone: '电话号码'
}
```

### 核心方法

**图片预览**:
```javascript
const previewImage = (current, urls) => {
  uni.previewImage({ current, urls })
}
```

**拨打电话**:
```javascript
const callWorker = (phone, name) => {
  uni.makePhoneCall({ phoneNumber: phone })
}
```

## 📋 文件说明

- `src/pages/order/detail.vue` - 主要实现文件
- `docs/safety-check-feature.md` - 安全检查功能详细说明
- `docs/worker-phone-feature.md` - 电话拨打功能详细说明
- `docs/implementation-summary.md` - 完整实现总结

## 🎨 样式特点

- 渐变背景设计
- 状态指示器（绿色✅/橙色⚠️）
- 可点击元素的视觉反馈
- 移动端适配的响应式布局
- 平滑的动画过渡效果

## 🔧 自定义配置

### 修改工作人员信息
在 `loadOrderDetail()` 方法中修改任务数据：

```javascript
{
  worker: '自定义姓名',
  workerPhone: '自定义电话'
}
```

### 修改安全检查问题
在安全检查任务中修改 `issues` 数组：

```javascript
issues: [
  '自定义问题1',
  '自定义问题2',
  '自定义问题3'
]
```

## 📞 联系支持

如有问题或建议，请查看详细文档或联系开发团队。

---

**注意**: 电话拨打功能需要在支持拨号的环境中使用（真机或支持拨号的模拟器）。
