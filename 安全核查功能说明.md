# 安全审查功能说明

## 功能概述

安全审查页面现在支持根据数据条数自动切换显示模式：

- **多条安全审查记录**：显示列表供用户选择
- **单条安全审查记录**：直接展示全部详细数据

## 主要功能

### 1. 多条记录列表模式
当有多条安全审查记录时，页面会显示：
- 安全审查记录列表
- 每条记录显示：核查时间、服务商名称、联系人
- 点击任意记录可查看详情

### 2. 单条记录详情模式
当只有一条安全审查记录时，页面会直接显示：
- 服务商信息
- 使用场景（含图片）
- 气瓶存放区（含图片）
- 报警器安装（含图片）
- 消防设备（含图片）
- 管道阀门安装图（含图片）
- 燃气设备使用图（含图片和描述）

### 3. 交互功能
- **列表选择**：在多条记录模式下，点击任意记录进入详情
- **返回列表**：在详情页面显示返回按钮，可返回列表
- **图片预览**：点击任意图片可全屏预览，支持滑动查看

## 技术实现

### 核心逻辑
```javascript
// 根据数据条数决定显示模式
<view v-else-if="safetyCheckList.length > 1 && !showBackButton" class="content">
  <!-- 多条记录列表 -->
</view>

<view v-else class="content">
  <!-- 单条记录详情 -->
</view>
```

### 状态管理
- `safetyCheckList`: 存储所有安全审查记录
- `safetyCheckData`: 当前选中的安全审查详情
- `showBackButton`: 控制是否显示返回按钮
- `loading`: 加载状态

### 主要方法
- `loadSafetyCheckData()`: 加载安全审查数据
- `selectSafetyCheck(checkData)`: 选择特定的安全审查记录
- `backToList()`: 返回列表视图
- `previewImage(currentImage, imageList)`: 预览图片

## 样式特点

- **现代化设计**：使用渐变背景和卡片式布局
- **响应式布局**：适配不同屏幕尺寸
- **交互反馈**：按钮点击效果和过渡动画
- **图片网格**：2列网格布局展示图片

## 测试功能

页面包含测试按钮，可以在单条和多条记录之间切换，方便测试不同场景下的显示效果。

## 使用场景

1. **餐厅/商户首次安全审查**：通常只有一条记录，直接显示详情
2. **多次核查的商户**：显示历史核查记录列表，用户可选择查看
3. **核查记录对比**：用户可以在不同时间的核查记录间切换查看

## 扩展建议

1. 可以添加核查记录的筛选和排序功能
2. 可以添加核查记录的导出功能
3. 可以添加核查记录的分享功能
4. 可以添加核查问题的标记和跟踪功能
