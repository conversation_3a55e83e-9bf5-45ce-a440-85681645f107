<template>
  <view class="container">
    <view class="main">
      <!-- 订单状态卡片 -->
      <view class="section status-section">
        <view class="status-header">
          <view class="status-icon" :class="`status-${order.status}`">
            <text class="icon">{{ getStatusIcon(order.status) }}</text>
          </view>
          <view class="status-info">
            <view class="status-text">{{ getStatusText(order.status) }}</view>
            <view class="status-desc">{{ getStatusDesc(order.status) }}</view>
          </view>
        </view>

        <!-- 订单进度 -->
        <view class="progress-timeline" v-if="order.status !== 'cancelled'">
          <view
            v-for="(step, index) in orderSteps"
            :key="index"
            :class="['timeline-item', {
              'completed': isStepCompleted(step.status),
              'current': order.status === step.status
            }]"
          >
            <view class="timeline-indicator">
              <view class="timeline-dot"></view>
              <view class="timeline-line" v-if="index < orderSteps.length - 1"></view>
            </view>
            <view class="timeline-content">
              <view class="timeline-title">{{ step.title }}</view>
              <view class="timeline-time" v-if="step.time">{{ formatTime(step.time) }}</view>
            </view>
          </view>
        </view>

        <!-- 配送员任务进度 -->
        <view class="task-progress" v-if="order.deliveryProgress">
          <view class="task-title">配送员任务进度</view>
          <view class="task-timeline">
            <view
              v-for="(task, index) in order.deliveryProgress"
              :key="index"
              :class="['task-item', {
                'completed': task.completed,
                'current': task.current,
                'pending': !task.completed && !task.current
              }]"
            >
              <view class="task-indicator">
                <view class="task-dot">
                  <text v-if="task.completed">✓</text>
                  <text v-else-if="task.current">●</text>
                </view>
                <view class="task-line" v-if="index < order.deliveryProgress.length - 1"></view>
              </view>
              <view class="task-content">
                <view class="task-name">{{ task.name }}</view>
                <view class="task-time" v-if="task.time">{{ formatTime(task.time) }}</view>
                <view class="task-desc" v-if="task.description">{{ task.description }}</view>
                <view class="task-worker" v-if="task.worker">配送员：{{ task.worker }}</view>
                <view class="task-location" v-if="task.location">当前位置：{{ task.location }}</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 维保员任务进度 -->
        <view class="task-progress" v-if="order.taskProgress">
          <view class="task-title">维保员任务进度</view>
          <view class="task-timeline">
            <view
              v-for="(task, index) in order.taskProgress"
              :key="index"
              :class="['task-item', {
                'completed': task.completed,
                'current': task.current,
                'pending': !task.completed && !task.current
              }]"
            >
              <view class="task-indicator">
                <view class="task-dot">
                  <text v-if="task.completed">✓</text>
                  <text v-else-if="task.current">●</text>
                </view>
                <view class="task-line" v-if="index < order.taskProgress.length - 1"></view>
              </view>
              <view class="task-content">
                <view class="task-name">{{ task.name }}</view>
                <view class="task-time" v-if="task.time">{{ formatTime(task.time) }}</view>
                <view class="task-desc" v-if="task.description">{{ task.description }}</view>
                <view class="task-worker" v-if="task.worker">维保员：{{ task.worker }}</view>

                <!-- 安装耗材费用支付 -->
                <view v-if="task.needPayment && !task.completed" class="payment-section">
                  <view class="payment-title">安装耗材费用 ¥{{ task.amount }}</view>
                  <view class="material-list">
                    <view v-for="(item, index) in task.materials" :key="index" class="material-item">
                      <text class="material-name">{{ item.name }}</text>
                      <text class="material-price">¥{{ item.price }}</text>
                    </view>
                  </view>
                  <button class="payment-btn" @click="payMaterialCost(task)">立即支付</button>
                </view>
                <view v-if="task.needPayment && task.completed" class="payment-completed">
                  <text class="completed-text">✓ 耗材费用已支付 ¥{{ task.amount }}</text>
                </view>

                <!-- 安全环境检查详情 -->
                <view v-if="task.name === '安全环境检查' && task.safetyCheck" class="safety-check-detail">
                  <view class="safety-status" :class="`status-${task.safetyCheck.status}`">
                    <text class="status-icon">{{ task.safetyCheck.status === 'qualified' ? '✓' : '⚠' }}</text>
                    <text class="status-text">
                      {{ task.safetyCheck.status === 'qualified' ? '安全环境合格' : '安全环境不合格' }}
                    </text>
                  </view>

                  <!-- 问题描述 -->
                  <view v-if="task.safetyCheck.issues && task.safetyCheck.issues.length > 0" class="safety-issues">
                    <view class="issues-title">发现问题：</view>
                    <view v-for="(issue, index) in task.safetyCheck.issues" :key="index" class="issue-item">
                      <text class="issue-text">{{ index + 1 }}. {{ issue }}</text>
                    </view>
                  </view>

                  <!-- 现场照片 -->
                  <view v-if="task.safetyCheck.images && task.safetyCheck.images.length > 0" class="safety-images">
                    <view class="images-title">现场照片：</view>
                    <view class="images-grid">
                      <image
                        v-for="(image, index) in task.safetyCheck.images"
                        :key="index"
                        :src="image"
                        mode="aspectFill"
                        class="safety-image"
                        @click="previewImage(image, task.safetyCheck.images)"
                      />
                    </view>
                  </view>
                </view>

                <!-- 上传资料查看入口 -->
                <view
                  v-if="task.name === '上传资料' && task.completed"
                  class="task-action"
                  @click="viewSafetyCheckDetail"
                >
                  <text class="action-text">查看安全检查详情</text>
                  <text class="action-arrow">→</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 订单基本信息 -->
      <view class="section">
        <view class="section-title">订单信息</view>
        <view class="order-info">
          <view class="info-row">
            <text class="info-label">订单编号</text>
            <text class="info-value">{{ order.orderNo }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">下单时间</text>
            <text class="info-value">{{ formatTime(order.createTime) }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">业务类型</text>
            <text class="info-value">{{ getBusinessTypeText(order.businessType) }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">联系人</text>
            <text class="info-value">{{ order.contactName }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">联系电话</text>
            <text class="info-value phone">{{ order.contactPhone }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">配送地址</text>
            <text class="info-value">{{ order.address }}</text>
          </view>
        </view>
      </view>

      <!-- 商品明细 -->
      <view class="section">
        <view class="section-title">商品明细</view>
        <view class="goods-list">
          <!-- 气瓶商品 -->
          <view v-for="(item, index) in order.items" :key="index" class="goods-item">
            <image :src="item.image" mode="aspectFit" class="goods-image"></image>
            <view class="goods-info">
              <view class="goods-name">{{ item.name }}</view>
              <view class="goods-spec">净重{{ item.netWeight }}</view>
              <view class="goods-price">
                ¥{{ item.price }}/个
                <text v-if="order.businessType === 1" class="deposit-text">(押金)</text>
              </view>
            </view>
            <view class="goods-quantity">
              <text>x{{ item.quantity }}</text>
              <text class="goods-total">¥{{ (item.price * item.quantity).toFixed(2) }}</text>
            </view>
          </view>

          <!-- 当没有气瓶时的提示 -->
          <view v-if="!order.items || order.items.length === 0" class="goods-item empty-item">
            <view class="empty-icon">📦</view>
            <view class="goods-info">
              <view class="goods-name">本订单仅租赁安全设备</view>
              <view class="goods-spec">未选择气瓶商品</view>
            </view>
          </view>
          
          <!-- 燃气报警器押金 -->
          <view v-if="order.gasAlarmDeposit" class="goods-item alarm-item">
            <image :src="getAlarmImage(order.userType)" mode="aspectFit" class="goods-image"></image>
            <view class="goods-info">
              <view class="goods-name">{{ order.gasAlarmDeposit.name || getAlarmName(order.userType) }}</view>
              <view class="goods-spec">押金</view>
              <view class="goods-price">¥{{ order.gasAlarmDeposit.price }}/个</view>
            </view>
            <view class="goods-quantity">
              <text>x{{ order.gasAlarmDeposit.quantity }}</text>
              <text class="goods-total">¥{{ (order.gasAlarmDeposit.price * order.gasAlarmDeposit.quantity).toFixed(2) }}</text>
            </view>
          </view>

          <!-- 一氧化碳报警器押金 -->
          <view v-if="order.coAlarmDeposit" class="goods-item alarm-item">
            <image :src="getCoAlarmImage()" mode="aspectFit" class="goods-image"></image>
            <view class="goods-info">
              <view class="goods-name">{{ order.coAlarmDeposit.name || getCoAlarmName() }}</view>
              <view class="goods-spec">押金</view>
              <view class="goods-price">¥{{ order.coAlarmDeposit.price }}/个</view>
            </view>
            <view class="goods-quantity">
              <text>x{{ order.coAlarmDeposit.quantity }}</text>
              <text class="goods-total">¥{{ (order.coAlarmDeposit.price * order.coAlarmDeposit.quantity).toFixed(2) }}</text>
            </view>
          </view>

          <!-- 兼容旧数据结构的报警器押金 -->
          <view v-if="order.alarmDeposit && !order.gasAlarmDeposit" class="goods-item alarm-item">
            <image :src="getAlarmImage(order.userType)" mode="aspectFit" class="goods-image"></image>
            <view class="goods-info">
              <view class="goods-name">{{ getAlarmName(order.userType) }}</view>
              <view class="goods-spec">押金</view>
              <view class="goods-price">¥{{ order.alarmDeposit.price }}/个</view>
            </view>
            <view class="goods-quantity">
              <text>x{{ order.alarmDeposit.quantity }}</text>
              <text class="goods-total">¥{{ (order.alarmDeposit.price * order.alarmDeposit.quantity).toFixed(2) }}</text>
            </view>
          </view>
          
          <!-- 配送费 -->
          <view v-if="order.deliveryFee > 0" class="goods-item fee-item">
            <view class="fee-icon">🚚</view>
            <view class="goods-info">
              <view class="goods-name">立即配送费</view>
              <view class="goods-spec">配送服务费</view>
            </view>
            <view class="goods-quantity">
              <text>x1</text>
              <text class="goods-total">¥{{ order.deliveryFee.toFixed(2) }}</text>
            </view>
          </view>
        </view>
        
        <view class="total-section">
          <view class="total-row">
            <text class="total-label">订单总额</text>
            <text class="total-value">¥{{ order.totalAmount.toFixed(2) }}</text>
          </view>
        </view>
      </view>

      <!-- 配送信息 -->
      <view class="section" v-if="order.delivery">
        <view class="section-title">配送信息</view>
        <view class="delivery-info">
          <view class="delivery-time">
            <view class="time-icon">📅</view>
            <view class="time-content">
              <view class="time-date">{{ order.delivery.date }}</view>
              <view class="time-slot">{{ order.delivery.time }}</view>
            </view>
          </view>
          <view class="delivery-status" v-if="order.deliveryStatus">
            <text class="status-text">{{ getDeliveryStatusText(order.deliveryStatus) }}</text>
          </view>
        </view>
      </view>

      <!-- 预约安装信息 -->
      <view class="section" v-if="order.installationAppointment">
        <view class="section-title">预约安装</view>
        <view class="appointment-info">
          <view class="appointment-time">
            <view class="time-icon">🔧</view>
            <view class="time-content">
              <view class="time-date">{{ order.installationAppointment.week }}</view>
              <view class="time-slot">{{ order.installationAppointment.time }}</view>
            </view>
            <view class="appointment-status" :class="`status-${order.appointmentStatus || 'pending'}`">
              {{ getAppointmentStatusText(order.appointmentStatus || 'pending') }}
            </view>
          </view>
          
          <!-- 安装师傅信息 -->
          <view v-if="order.installer" class="installer-info">
            <view class="installer-avatar">👨‍🔧</view>
            <view class="installer-details">
              <view class="installer-name">{{ order.installer.name }}</view>
              <view class="installer-phone">{{ order.installer.phone }}</view>
            </view>
            <button class="contact-btn" @click="contactInstaller">联系师傅</button>
          </view>
        </view>
      </view>

      <!-- 取消原因 -->
      <view class="section" v-if="order.status === 'cancelled' && order.cancelReason">
        <view class="section-title">取消原因</view>
        <view class="cancel-info">
          <view class="cancel-reason">{{ order.cancelReason }}</view>
          <view class="cancel-time">取消时间：{{ formatTime(order.cancelTime) }}</view>
        </view>
      </view>
    </view>

    <!-- 演示切换按钮 -->
    <view class="demo-bar">
      <button class="demo-btn" @click="toggleSafetyCheckStatus">
        切换安全检查状态演示 (当前: {{ getCurrentSafetyStatus() }})
      </button>
    </view>

    <!-- 底部操作栏 -->
    <view class="action-bar" v-if="showActionButtons">
      <button
        v-if="order.status === 'pending'"
        class="btn btn-outline"
        @click="cancelOrder"
      >
        取消订单
      </button>
      <button
        v-if="order.status === 'pending'"
        class="btn btn-primary"
        @click="payOrder"
      >
        立即支付
      </button>
      <button
        v-if="order.status === 'processing' && hasDeliveryItems"
        class="btn btn-outline"
        @click="modifyDelivery"
      >
        修改配送
      </button>
      <button
        v-if="order.status === 'processing'"
        class="btn btn-outline"
        @click="trackDelivery"
      >
        查看物流
      </button>
      <button
        v-if="order.status === 'installing' && order.businessType === 1 && hasAlarmItems"
        class="btn btn-primary"
        @click="confirmInstallation"
      >
        确认安装完成
      </button>


      <button
        v-if="order.status === 'afterSale'"
        class="btn btn-outline"
        @click="viewAfterSale"
      >
        查看详情
      </button>

      <!-- 配送失败状态按钮 -->
      <button
        v-if="order.status === 'deliveryFailed'"
        class="btn btn-outline"
        @click="contactService"
      >
        联系客服
      </button>
      <button
        v-if="order.status === 'deliveryFailed'"
        class="btn btn-primary"
        @click="rescheduleDelivery"
      >
        重新配送
      </button>

      <!-- 安装失败状态按钮 -->
      <button
        v-if="order.status === 'installFailed'"
        class="btn btn-outline"
        @click="contactService"
      >
        联系客服
      </button>
      <button
        v-if="order.status === 'installFailed'"
        class="btn btn-primary"
        @click="rescheduleInstall"
      >
        重新安装
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const orderId = ref(null)
const order = ref({
  orderNo: '',
  status: 'pending',
  businessType: 2,
  createTime: '',
  payTime: '',
  deliveryTime: '',
  completeTime: '',
  totalAmount: 0,
  contactName: '',
  contactPhone: '',
  address: '',
  items: [],
  delivery: null,
  installationAppointment: null,
  alarmDeposit: null,
  gasAlarmDeposit: null,
  coAlarmDeposit: null,
  deliveryFee: 0,
  userType: 'resident',
  deliveryStatus: null,
  appointmentStatus: null,
  installer: null,
  cancelReason: '',
  cancelTime: ''
})
const orderSteps = ref([])

// 计算属性
const showActionButtons = computed(() => {
  return ['pending', 'processing', 'installing', 'afterSale', 'deliveryFailed', 'installFailed'].includes(order.value.status)
})

// 是否有需要配送的商品（气瓶）
const hasDeliveryItems = computed(() => {
  return order.value.items && order.value.items.length > 0
})

// 是否有报警器需要安装
const hasAlarmItems = computed(() => {
  return order.value.gasAlarmDeposit || order.value.coAlarmDeposit || order.value.alarmDeposit
})

// 页面加载时的处理
onMounted(() => {
  // 模拟获取订单ID，实际应用中通过路由参数获取
  // 这里先使用固定ID进行演示
  orderId.value = '1'
  loadOrderDetail()
})

// 加载订单详情
const loadOrderDetail = () => {
  uni.showLoading({
    title: '加载中'
  })

  // 模拟API调用
  setTimeout(() => {
    // 模拟订单数据 - 根据订单ID决定显示合格还是不合格的案例
    const isQualifiedCase = orderId.value === '1'

    order.value = {
      orderNo: orderId.value,
      status: 'installing',
      businessType: 1, // 1: 租赁, 2: 换气
      createTime: '2024-12-01T10:30:00.000Z',
      payTime: '2024-12-01T10:35:00.000Z',
      deliveryTime: '2024-12-02T14:20:00.000Z',
      totalAmount: 470,
      contactName: '张师傅',
      contactPhone: '************',
      address: '广州市海珠区新港东路123号',
      userType: 'resident',
      items: [
        {
          id: 1,
          name: '5kg气瓶',
          netWeight: '4kg',
          price: 70,
          quantity: 2,
          image: '/static/images/5kg.jpg'
        },
        {
          id: 2,
          name: '15kg气瓶',
          netWeight: '12.5kg',
          price: 130,
          quantity: 2,
          image: '/static/images/15kg.jpg'
        }
      ],
      alarmDeposit: {
        quantity: 1,
        price: 100
      },
      installationAppointment: {
        date: '2024-12-15',
        week: '12月15日 周日',
        time: '上午 9:00-11:00'
      },
      appointmentStatus: 'confirmed',
      installer: {
        name: '李师傅',
        phone: '13900139000'
      },
      deliveryFee: 0,
      // 配送员任务进度
      deliveryProgress: [
        {
          name: '接单配送',
          completed: true,
          current: false,
          time: '2024-12-02T09:00:00.000Z',
          description: '配送员已接单，准备配送',
          worker: '王师傅'
        },
        {
          name: '商品出库',
          completed: true,
          current: false,
          time: '2024-12-02T09:30:00.000Z',
          description: '商品已从仓库出库装车',
          worker: '王师傅'
        },
        {
          name: '配送途中',
          completed: true,
          current: false,
          time: '2024-12-02T10:00:00.000Z',
          description: '配送员已出发，正在配送途中',
          worker: '王师傅',
          location: '距离目的地约5公里'
        },
        {
          name: '即将到达',
          completed: false,
          current: true,
          time: null,
          description: '配送员即将到达配送地址',
          worker: '王师傅',
          location: '距离目的地约1公里'
        },
        {
          name: '送达签收',
          completed: false,
          current: false,
          time: null,
          description: '客户签收确认',
          worker: '王师傅'
        }
      ],
      // 维保员任务进度
      taskProgress: [
        {
          name: '任务接受',
          completed: true,
          current: false,
          time: '2024-12-02T15:00:00.000Z',
          description: '维保员已接受安装任务',
          worker: '李师傅'
        },
        {
          name: '前往现场',
          completed: true,
          current: false,
          time: '2024-12-02T15:30:00.000Z',
          description: '维保员已出发前往安装现场',
          worker: '李师傅'
        },
        {
          name: '开始作业',
          completed: true,
          current: false,
          time: '2024-12-02T16:00:00.000Z',
          description: '维保员已到达现场，开始安装作业',
          worker: '李师傅'
        },
        {
          name: '安全环境检查',
          completed: true,
          current: false,
          time: '2024-12-02T16:15:00.000Z',
          description: isQualifiedCase ? '现场安全环境检查合格' : '现场安全环境检查不合格，存在安全隐患',
          worker: '李师傅',
          safetyCheck: {
            status: isQualifiedCase ? 'qualified' : 'unqualified', // qualified: 合格, unqualified: 不合格
            issues: isQualifiedCase ? [] : [
              '燃气管道老化，存在漏气风险',
              '通风不良，空气流通不畅',
              '周围有易燃物品堆放'
            ], // 问题描述数组
            images: ['/static/images/safety-check-1.jpg', '/static/images/safety-check-2.jpg'] // 现场照片
          }
        },
        {
          name: '上传资料',
          completed: true,
          current: false,
          time: '2024-12-02T17:00:00.000Z',
          description: '已上传安装照片和相关资料',
          worker: '李师傅'
        },
        {
          name: '后台审核',
          completed: false,
          current: true,
          time: null,
          description: '正在审核安装质量和安全检查资料',
          worker: null
        },
        {
          name: '安装耗材费用',
          completed: false,
          current: false,
          time: null,
          description: '需要支付安装过程中使用的耗材费用',
          worker: null,
          needPayment: true,
          amount: 50.00,
          materials: [
            { name: '燃气软管', price: 25.00 },
            { name: '密封胶', price: 15.00 },
            { name: '管件配件', price: 10.00 }
          ]
        },
        {
          name: '完成作业',
          completed: false,
          current: false,
          time: null,
          description: '安装作业完成确认',
          worker: '李师傅'
        },
        {
          name: '客户电子签名确认',
          completed: false,
          current: false,
          time: null,
          description: '客户确认安装完成并电子签名',
          worker: null
        }
      ]
    }

    initOrderSteps()
    uni.hideLoading()
  }, 1000)
}

// 初始化订单步骤
const initOrderSteps = () => {
  if (order.value.businessType === 1) {
    // 租赁业务步骤
    orderSteps.value = [
      { status: 'pending', title: '订单确认', time: order.value.createTime },
      { status: 'processing', title: '支付完成', time: order.value.payTime },
      { status: 'installing', title: '商品送达', time: order.value.deliveryTime },
      { status: 'completed', title: '安装完成', time: order.value.completeTime }
    ]
  } else {
    // 换气业务步骤
    orderSteps.value = [
      { status: 'pending', title: '订单确认', time: order.value.createTime },
      { status: 'processing', title: '支付完成', time: order.value.payTime },
      { status: 'completed', title: '配送完成', time: order.value.deliveryTime }
    ]
  }
}

// 获取状态图标
const getStatusIcon = (status) => {
  const iconMap = {
    'pending': '⏳',
    'processing': '🚚',
    'installing': '🔧',
    'completed': '✅',
    'afterSale': '🔄',
    'cancelled': '❌',
    'deliveryFailed': '⚠️',
    'installFailed': '⚠️'
  }
  return iconMap[status] || '❓'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待付款',
    'processing': '待配送',
    'installing': '安装/维修',
    'completed': '已完成',
    'afterSale': '售后/退款',
    'cancelled': '已取消',
    'deliveryFailed': '配送失败',
    'installFailed': '安装失败'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态描述
const getStatusDesc = (status) => {
  const descMap = {
    'pending': '请及时完成支付',
    'processing': '订单已支付，准备配送',
    'installing': hasAlarmItems.value ? '商品已送达，等待安装' : '准备上门维修',
    'completed': '订单已完成',
    'afterSale': '售后处理中',
    'cancelled': '订单已取消',
    'deliveryFailed': '配送过程中出现问题，请联系客服',
    'installFailed': '安装过程中出现问题，请联系客服'
  }
  return descMap[status] || ''
}

// 获取业务类型文本
const getBusinessTypeText = (type) => {
  return type === 1 ? '租赁' : '换气'
}

// 获取燃气报警器名称
const getAlarmName = (userType) => {
  return userType === 'business-enterprise' ? '商用燃气报警器' : '家用燃气报警器'
}

// 获取燃气报警器图片
const getAlarmImage = (userType) => {
  return userType === 'business-enterprise'
    ? '/static/images/commercial-alarm.png'
    : '/static/images/home-alarm.png'
}

// 获取一氧化碳报警器名称
const getCoAlarmName = () => {
  return '一氧化碳报警器'
}

// 获取一氧化碳报警器图片
const getCoAlarmImage = () => {
  return '/static/images/carbon_monoxide_alarm.jpg'
}

// 获取配送状态文本
const getDeliveryStatusText = (status) => {
  const statusMap = {
    'preparing': '准备中',
    'shipping': '配送中',
    'delivered': '已送达'
  }
  return statusMap[status] || '待配送'
}

// 获取预约状态文本
const getAppointmentStatusText = (status) => {
  const statusMap = {
    'pending': '待确认',
    'confirmed': '已确认',
    'in-service': '服务中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '待确认'
}

// 判断步骤是否完成
const isStepCompleted = (stepStatus) => {
  const statusOrder = ['pending', 'processing', 'installing', 'completed']
  const currentIndex = statusOrder.indexOf(order.value.status)
  const stepIndex = statusOrder.indexOf(stepStatus)
  return stepIndex <= currentIndex
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 联系安装师傅
const contactInstaller = () => {
  if (order.value.installer && order.value.installer.phone) {
    uni.makePhoneCall({
      phoneNumber: order.value.installer.phone
    })
  }
}

// 取消订单
const cancelOrder = () => {
  uni.showModal({
    title: '取消订单',
    content: '确定要取消这个订单吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({
          title: '取消中...'
        })

        // 模拟API调用
        setTimeout(() => {
          uni.hideLoading()
          order.value.status = 'cancelled'
          order.value.cancelReason = '用户主动取消'
          order.value.cancelTime = new Date().toISOString()

          uni.showToast({
            title: '订单已取消',
            icon: 'success'
          })
        }, 1000)
      }
    }
  })
}

// 修改配送
const modifyDelivery = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}

// 确认安装完成
const confirmInstallation = () => {
  uni.showModal({
    title: '确认安装完成',
    content: '确认报警器已安装完成？',
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({
          title: '确认中...'
        })

        // 模拟API调用
        setTimeout(() => {
          uni.hideLoading()
          order.value.status = 'completed'
          order.value.completeTime = new Date().toISOString()
          initOrderSteps()

          uni.showToast({
            title: '安装确认成功',
            icon: 'success'
          })
        }, 1000)
      }
    }
  })
}



// 立即支付
const payOrder = () => {
  uni.showToast({
    title: '跳转支付页面',
    icon: 'none'
  })
}

// 查看物流
const trackDelivery = () => {
  uni.showToast({
    title: '查看物流信息',
    icon: 'none'
  })
}

// 查看售后详情
const viewAfterSale = () => {
  uni.showToast({
    title: '查看售后详情',
    icon: 'none'
  })
}

// 查看安全检查详情
const viewSafetyCheckDetail = () => {
  uni.navigateTo({
    url: '/pages/store/safety-check-detail'
  })
}

// 预览图片
const previewImage = (current, urls) => {
  uni.previewImage({
    current: current,
    urls: urls
  })
}

// 切换安全检查状态演示
const toggleSafetyCheckStatus = () => {
  const safetyCheckTask = order.value.taskProgress.find(task => task.name === '安全环境检查')
  if (safetyCheckTask && safetyCheckTask.safetyCheck) {
    const isCurrentlyQualified = safetyCheckTask.safetyCheck.status === 'qualified'

    // 切换状态
    safetyCheckTask.safetyCheck.status = isCurrentlyQualified ? 'unqualified' : 'qualified'
    safetyCheckTask.description = isCurrentlyQualified
      ? '现场安全环境检查不合格，存在安全隐患'
      : '现场安全环境检查合格'

    // 切换问题列表
    safetyCheckTask.safetyCheck.issues = isCurrentlyQualified ? [
      '燃气管道老化，存在漏气风险',
      '通风不良，空气流通不畅',
      '周围有易燃物品堆放'
    ] : []

    uni.showToast({
      title: `已切换为${isCurrentlyQualified ? '不合格' : '合格'}状态`,
      icon: 'none'
    })
  }
}

// 获取当前安全检查状态
const getCurrentSafetyStatus = () => {
  const safetyCheckTask = order.value.taskProgress.find(task => task.name === '安全环境检查')
  if (safetyCheckTask && safetyCheckTask.safetyCheck) {
    return safetyCheckTask.safetyCheck.status === 'qualified' ? '合格' : '不合格'
  }
  return '未知'
}

// 支付耗材费用
const payMaterialCost = (task) => {
  uni.showModal({
    title: '支付确认',
    content: `确认支付安装耗材费用 ¥${task.amount} 吗？`,
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({
          title: '支付中...'
        })

        // 模拟支付过程
        setTimeout(() => {
          // 更新任务状态为已完成
          task.completed = true
          task.current = false
          task.time = new Date().toISOString()

          // 将下一个步骤设为当前步骤
          const taskIndex = order.value.taskProgress.findIndex(t => t.needPayment)
          if (taskIndex < order.value.taskProgress.length - 1) {
            order.value.taskProgress[taskIndex + 1].current = true
          }

          uni.hideLoading()
          uni.showToast({
            title: '支付成功',
            icon: 'success'
          })
        }, 2000)
      }
    }
  })
}

// 联系客服
const contactService = () => {
  uni.showModal({
    title: '联系客服',
    content: '客服电话：************\n工作时间：9:00-18:00',
    confirmText: '拨打电话',
    success: (res) => {
      if (res.confirm) {
        uni.makePhoneCall({
          phoneNumber: '************'
        })
      }
    }
  })
}

// 重新配送
const rescheduleDelivery = () => {
  uni.showModal({
    title: '重新配送',
    content: '确认重新安排配送吗？我们会尽快为您重新配送。',
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({
          title: '处理中...'
        })

        setTimeout(() => {
          order.value.status = 'processing'
          uni.hideLoading()
          uni.showToast({
            title: '已重新安排配送',
            icon: 'success'
          })
        }, 2000)
      }
    }
  })
}

// 重新安装
const rescheduleInstall = () => {
  uni.showModal({
    title: '重新安装',
    content: '确认重新安排安装吗？我们会安排师傅重新上门安装。',
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({
          title: '处理中...'
        })

        setTimeout(() => {
          order.value.status = 'installing'
          uni.hideLoading()
          uni.showToast({
            title: '已重新安排安装',
            icon: 'success'
          })
        }, 2000)
      }
    }
  })
}


</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 120rpx;
}

.main {
  padding: 32rpx 24rpx;
}

.section {
  margin-bottom: 30rpx;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  border-left: 8rpx solid var(--primary-color);
}

/* 状态卡片样式 */
.status-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  background-color: rgba(255, 255, 255, 0.2);
}

.status-icon .icon {
  font-size: 36rpx;
}

.status-info {
  flex: 1;
}

.status-text {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.status-desc {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 进度时间线样式 */
.progress-timeline {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
}

.timeline-indicator {
  position: relative;
  margin-right: 20rpx;
}

.timeline-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
}

.timeline-item.completed .timeline-dot,
.timeline-item.current .timeline-dot {
  background-color: white;
}

.timeline-line {
  position: absolute;
  left: 50%;
  top: 20rpx;
  width: 2rpx;
  height: 40rpx;
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateX(-50%);
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.timeline-time {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 订单信息样式 */
.order-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
}

.info-label {
  color: #666;
  min-width: 120rpx;
}

.info-value {
  color: #333;
  font-weight: 500;
  text-align: right;
  flex: 1;
  word-break: break-all;
}

.phone {
  color: var(--primary-color);
}

/* 商品列表样式 */
.goods-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.goods-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.goods-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  background-color: #fff;
}

.fee-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background-color: #fff;
  border-radius: 8rpx;
  margin-right: 16rpx;
}

.goods-info {
  flex: 1;
}

.goods-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.goods-spec {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.goods-price {
  font-size: 24rpx;
  color: #666;
}

.goods-quantity {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 26rpx;
  color: #666;
}

.goods-total {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-top: 4rpx;
}

.total-section {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 32rpx;
}

.total-label {
  color: #333;
  font-weight: bold;
}

.total-value {
  color: #ff6700;
  font-size: 36rpx;
  font-weight: bold;
}

/* 配送信息样式 */
.delivery-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.delivery-time {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f0f9ff;
  border-radius: 12rpx;
}

.time-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
}

.time-content {
  flex: 1;
}

.time-date {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.time-slot {
  font-size: 24rpx;
  color: #666;
}

.delivery-status {
  text-align: center;
  padding: 12rpx;
  background-color: #e6f7ff;
  border-radius: 8rpx;
}

.delivery-status .status-text {
  font-size: 26rpx;
  color: #1890ff;
  font-weight: 500;
}

/* 预约安装样式 */
.appointment-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.appointment-time {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f6ffed;
  border-radius: 12rpx;
}

.appointment-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.appointment-status.status-pending {
  background-color: #fff7e6;
  color: #fa8c16;
}

.appointment-status.status-confirmed {
  background-color: #e6f7ff;
  color: #1890ff;
}

.appointment-status.status-completed {
  background-color: #f6ffed;
  color: #52c41a;
}

.installer-info {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f0f9ff;
  border-radius: 12rpx;
}

.installer-avatar {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.installer-details {
  flex: 1;
}

.installer-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.installer-phone {
  font-size: 26rpx;
  color: #1890ff;
}

.contact-btn {
  padding: 12rpx 24rpx;
  background-color: #1890ff;
  color: white;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
}

/* 取消信息样式 */
.cancel-info {
  padding: 20rpx;
  background-color: #fff2f0;
  border-radius: 12rpx;
  border-left: 4rpx solid #ff4d4f;
}

.cancel-reason {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.cancel-time {
  font-size: 24rpx;
  color: #999;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 20rpx;
}

/* H5端兼容性 */
/* #ifdef H5 */
.action-bar {
  padding-bottom: 40rpx;
}
/* #endif */

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.btn-outline {
  background-color: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.btn-primary {
  padding: 0;
  background-color: var(--primary-color);
  color: white;
}

/* 空商品提示样式 */
.empty-item {
  background-color: #f8f9fa;
  border: 1rpx dashed #dee2e6;
}

.empty-icon {
  font-size: 48rpx;
  opacity: 0.6;
}

.deposit-text {
  font-size: 24rpx;
  color: #999;
  font-weight: 400;
}

.btn::after {
  border: none;
}

/* 维保员任务进度样式 */
.task-progress {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.task-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 20rpx;
}

.task-timeline {
  display: flex;
  flex-direction: column;
}

.task-item {
  display: flex;
  align-items: stretch;
  min-height: 80rpx;
}

.task-indicator {
  position: relative;
  margin-right: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100%;
}

.task-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14rpx;
  font-weight: bold;
}

.task-item.completed .task-dot {
  background-color: #52c41a;
  color: #fff;
}

.task-item.current .task-dot {
  background-color: #1890ff;
  color: #fff;
  animation: pulse 2s infinite;
}

.task-item.pending .task-dot {
  background-color: rgba(255, 255, 255, 0.3);
  border: 2rpx solid rgba(255, 255, 255, 0.5);
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

.task-line {
  width: 2rpx;
  flex: 1;
  min-height: 60rpx;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 8rpx 0;
}

.task-item.completed .task-line {
  background-color: #52c41a;
}

.task-item:last-child .task-indicator {
  min-height: auto;
}

.task-item:last-child .task-content {
  padding-bottom: 0;
}

.task-content {
  flex: 1;
  min-width: 0;
  padding-bottom: 20rpx;
}

.task-name {
  font-size: 26rpx;
  font-weight: 500;
  color: #fff;
  margin-bottom: 4rpx;
}

.task-time {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4rpx;
}

.task-desc {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
  margin-bottom: 4rpx;
}

.task-worker {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
}

.task-item.current .task-name {
  color: #1890ff;
  font-weight: 600;
}

.task-item.current .task-desc {
  color: rgba(24, 144, 255, 0.8);
}

.task-location {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 4rpx;
}

/* 任务操作按钮 */
.task-action {
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 12rpx;
  padding: 12rpx 16rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.task-action:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.98);
}

.action-text {
  margin-right: 10rpx;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.action-arrow {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
  font-weight: bold;
}

/* 支付相关样式 */
.payment-section {
  margin-top: 16rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.payment-title {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  margin-bottom: 16rpx;
}

.material-list {
  margin-bottom: 16rpx;
}

.material-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
  font-size: 22rpx;
}

.material-name {
  color: rgba(255, 255, 255, 0.7);
}

.material-price {
  color: #ffc107;
  font-weight: 500;
}

.payment-btn {
  padding: 8rpx 20rpx;
  background: #ffc107;
  color: #333;
  border-radius: 6rpx;
  font-size: 22rpx;
  border: none;
  font-weight: 500;
  align-self: flex-start;
}

.payment-btn:active {
  background: #ffb300;
}

.payment-completed {
  margin-top: 16rpx;
  padding: 16rpx 20rpx;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 12rpx;
  border: 1rpx solid rgba(76, 175, 80, 0.2);
}

.completed-text {
  font-size: 22rpx;
  color: #4caf50;
  font-weight: 500;
}

/* 安全环境检查样式 */
.safety-check-detail {
  margin-top: 16rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.safety-status {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
}

.safety-status.status-qualified {
  background: rgba(76, 175, 80, 0.1);
  border: 1rpx solid rgba(76, 175, 80, 0.2);
}

.safety-status.status-unqualified {
  background: rgba(255, 152, 0, 0.1);
  border: 1rpx solid rgba(255, 152, 0, 0.2);
}

.status-icon {
  font-size: 20rpx;
  margin-right: 8rpx;
  font-weight: bold;
}

.safety-status.status-qualified .status-icon {
  color: #4caf50;
}

.safety-status.status-unqualified .status-icon {
  color: #ff9800;
}

.status-text {
  font-size: 22rpx;
  font-weight: 500;
}

.safety-status.status-qualified .status-text {
  color: #4caf50;
}

.safety-status.status-unqualified .status-text {
  color: #ff9800;
}

.safety-issues {
  margin-bottom: 16rpx;
}

.issues-title {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  margin-bottom: 8rpx;
}

.issue-item {
  margin-bottom: 6rpx;
}

.issue-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

.safety-images {
  margin-top: 16rpx;
}

.images-title {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  margin-bottom: 12rpx;
}

.images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.safety-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background-color: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.safety-image:active {
  transform: scale(0.95);
}

/* 演示按钮样式 */
.demo-bar {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  z-index: 1000;
}

.demo-btn {
  padding: 16rpx 24rpx;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
  backdrop-filter: blur(10rpx);
}

.demo-btn:active {
  background: rgba(0, 0, 0, 0.8);
}
</style>
