<template>
  <view class="container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 安全审查列表 -->
    <view v-else class="content">
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>安全审查记录</text>
        </view>
        <view class="check-list">
          <view
            v-for="(item, index) in safetyCheckList"
            :key="index"
            class="check-item"
            @click="goToDetail(item, index)"
          >
            <view class="check-item-header">
              <text class="check-item-title">核查记录 {{ index + 1 }}</text>
              <text class="check-item-time">{{ item.serviceProvider.checkTime }}</text>
            </view>
            <view class="check-item-info">
              <text class="check-item-provider">{{ item.serviceProvider.name }}</text>
              <text class="check-item-contact">{{ item.serviceProvider.contact }}</text>
            </view>
            <view class="check-item-arrow">
              <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const storeId = ref(null)
const loading = ref(true)
const safetyCheckList = ref([])

// 页面加载时的处理
const onLoad = (options) => {
  storeId.value = options.id;
  loadSafetyCheckList();
}

// 加载安全审查列表数据
const loadSafetyCheckList = () => {
  loading.value = true;

  // 模拟API调用
  setTimeout(() => {
    // 模拟多条安全审查数据
    const mockData = [
      {
        id: 1,
        serviceProvider: {
          name: "广州市燃气安全检测有限公司",
          contact: "李工程师",
          phone: "020-12345678",
          checkTime: "2023-10-15 14:30"
        }
      },
      {
        id: 2,
        serviceProvider: {
          name: "深圳市安全检测中心",
          contact: "王工程师",
          phone: "0755-87654321",
          checkTime: "2023-09-20 10:15"
        }
      },
      {
        id: 3,
        serviceProvider: {
          name: "东莞市燃气检测服务中心",
          contact: "张工程师",
          phone: "0769-88888888",
          checkTime: "2023-08-10 16:20"
        }
      }
    ];

    safetyCheckList.value = mockData;
    loading.value = false;
  }, 1000);
}

// 跳转到详情页面
const goToDetail = (checkData, index) => {
  uni.navigateTo({
    url: `/pages/store/safety-check-detail?storeId=${storeId.value}&checkId=${checkData.id}&index=${index}`
  });
}

// 直接加载数据
loadSafetyCheckList();

// 导出页面生命周期方法供uni-app使用
defineExpose({
  onLoad
})
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #4c6ef5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* 内容区域 */
.content {
  padding: 30rpx;
}

.section {
  margin-bottom: 30rpx;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
}

.section-title-bar {
  width: 6rpx;
  height: 30rpx;
  background-color: #4c6ef5;
  margin-right: 15rpx;
  border-radius: 3rpx;
}

.section-title text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 安全审查列表 */
.check-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.check-item {
  background-color: #fff;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  transition: all 0.2s ease;
}

.check-item:active {
  transform: scale(0.99);
  box-shadow: 0 1rpx 8rpx rgba(0, 0, 0, 0.08);
}

.check-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.check-item-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.check-item-time {
  font-size: 24rpx;
  color: #999;
}

.check-item-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.check-item-provider {
  font-size: 28rpx;
  color: #4c6ef5;
  font-weight: 500;
}

.check-item-contact {
  font-size: 26rpx;
  color: #666;
}

.check-item-arrow {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
}

/* 响应式布局 */
@media (max-width: 600rpx) {
  .content {
    padding: 20rpx;
  }

  .section {
    margin-bottom: 25rpx;
  }

  .check-item {
    padding: 25rpx;
  }
}
</style>
