<template>
  <view class="container">
    <!-- 状态筛选标签 -->
    <view class="filter-tabs">
      <view 
        v-for="(tab, index) in filterTabs" 
        :key="index"
        :class="['tab-item', { active: activeTab === tab.value }]"
        @click="switchTab(tab.value)"
      >
        {{ tab.label }}
        <view class="badge" v-if="tab.count > 0">{{ tab.count }}</view>
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list" v-if="filteredOrders.length > 0">
      <view 
        v-for="order in filteredOrders" 
        :key="order.id"
        class="order-item"
        @click="goToDetail(order.id)"
      >
        <!-- 订单头部 -->
        <view class="order-header">
          <view class="order-info">
            <text class="order-no">订单号：{{ order.orderNo }}</text>
            <text class="order-time">{{ formatTime(order.createTime) }}</text>
          </view>
          <view class="order-status" :class="`status-${order.status}`">
            {{ getStatusText(order.status) }}
          </view>
        </view>

        <!-- 商品信息 -->
        <view class="goods-section">
          <view v-for="(item, index) in order.items" :key="index" class="goods-item">
            <image :src="item.image" mode="aspectFit" class="goods-image"></image>
            <view class="goods-info">
              <view class="goods-name">{{ item.name }}</view>
              <view class="goods-spec">净重{{ item.netWeight }}</view>
              <view class="goods-quantity">x{{ item.quantity }}</view>
            </view>
            <view class="goods-price">¥{{ item.price }}</view>
          </view>
        </view>

        <!-- 订单底部 -->
        <view class="order-footer">
          <view class="total-info">
            <text class="total-text">共{{ getTotalQuantity(order.items) }}件商品 合计：</text>
            <text class="total-price">¥{{ order.totalAmount }}</text>
          </view>
          <view class="action-buttons">
            <button 
              v-if="order.status === 'pending'" 
              class="btn btn-outline" 
              @click.stop="cancelOrder(order.id)"
            >
              取消订单
            </button>
            <button 
              v-if="order.status === 'pending'" 
              class="btn btn-primary" 
              @click.stop="payOrder(order.id)"
            >
              立即支付
            </button>
            <button 
              v-if="order.status === 'processing'" 
              class="btn btn-outline" 
              @click.stop="trackDelivery(order.id)"
            >
              查看物流
            </button>
            <button
              v-if="order.status === 'installing'"
              class="btn btn-outline"
              @click.stop="contactInstaller(order.id)"
            >
              联系师傅
            </button>
            <button
              v-if="order.status === 'repairing'"
              class="btn btn-outline"
              @click.stop="contactRepairman(order.id)"
            >
              联系维修师傅
            </button>
            <button 
              v-if="order.status === 'completed'" 
              class="btn btn-outline" 
              @click.stop="reorder(order.id)"
            >
              再次下单
            </button>
            <button 
              v-if="order.status === 'afterSale'" 
              class="btn btn-outline" 
              @click.stop="viewAfterSale(order.id)"
            >
              查看详情
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <view class="empty-icon">📋</view>
      <text class="empty-text">暂无相关订单</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const activeTab = ref('all')
const orderList = ref([])

// 筛选标签
const filterTabs = ref([
  { label: '全部', value: 'all', count: 0 },
  { label: '待付款', value: 'pending', count: 0 },
  { label: '待配送', value: 'processing', count: 0 },
  { label: '安装', value: 'installing', count: 0 },
  { label: '维修', value: 'repairing', count: 0 },
  { label: '已完成', value: 'completed', count: 0 },
  { label: '售后/退款', value: 'afterSale', count: 0 }
])

// 计算属性
const filteredOrders = computed(() => {
  if (activeTab.value === 'all') {
    return orderList.value
  }
  return orderList.value.filter(order => order.status === activeTab.value)
})

// 页面加载
onMounted(() => {
  // 模拟获取页面参数，实际应用中通过路由参数获取
  // 这里可以根据需要设置默认的activeTab
  loadOrderList()
})

// 加载订单列表
const loadOrderList = () => {
  uni.showLoading({
    title: '加载中'
  })

  // 模拟API请求
  setTimeout(() => {
    orderList.value = [
      {
        id: 1,
        orderNo: 'GAS202401001',
        status: 'pending',
        createTime: '2024-01-15 10:30:00',
        totalAmount: 280.00,
        items: [
          {
            name: '5kg气瓶',
            netWeight: '4kg',
            quantity: 2,
            price: 70,
            image: '/static/images/5kg.jpg'
          },
          {
            name: '15kg气瓶',
            netWeight: '12.5kg',
            quantity: 1,
            price: 130,
            image: '/static/images/15kg.jpg'
          }
        ]
      },
      {
        id: 2,
        orderNo: 'GAS202401002',
        status: 'processing',
        createTime: '2024-01-14 14:20:00',
        totalAmount: 380.00,
        items: [
          {
            name: '50kg气瓶',
            netWeight: '45kg',
            quantity: 1,
            price: 380,
            image: '/static/images/50kg.jpg'
          }
        ]
      },
      {
        id: 3,
        orderNo: 'GAS202401003',
        status: 'installing',
        createTime: '2024-01-13 09:15:00',
        totalAmount: 200.00,
        items: [
          {
            name: '家用燃气报警器',
            netWeight: '0.5kg',
            quantity: 1,
            price: 100,
            image: '/static/images/home-alarm.png'
          },
          {
            name: '一氧化碳报警器',
            netWeight: '0.3kg',
            quantity: 1,
            price: 120,
            image: '/static/images/carbon_monoxide_alarm.jpg'
          }
        ]
      },
      {
        id: 5,
        orderNo: 'GAS202401005',
        status: 'repairing',
        createTime: '2024-01-11 11:30:00',
        totalAmount: 80.00,
        items: [
          {
            name: '燃气灶维修',
            netWeight: '-',
            quantity: 1,
            price: 80,
            image: '/static/images/repair-service.png'
          }
        ]
      },
      {
        id: 4,
        orderNo: 'GAS202401004',
        status: 'completed',
        createTime: '2024-01-12 16:45:00',
        totalAmount: 140.00,
        items: [
          {
            name: '15kg气瓶',
            netWeight: '12.5kg',
            quantity: 1,
            price: 130,
            image: '/static/images/15kg.jpg'
          }
        ]
      }
    ]

    // 更新标签计数
    updateTabCounts()
    uni.hideLoading()
  }, 1000)
}

// 更新标签计数
const updateTabCounts = () => {
  filterTabs.value.forEach(tab => {
    if (tab.value === 'all') {
      tab.count = orderList.value.length
    } else {
      tab.count = orderList.value.filter(order => order.status === tab.value).length
    }
  })
}

// 切换标签
const switchTab = (tabValue) => {
  activeTab.value = tabValue
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待付款',
    'processing': '待配送',
    'installing': '安装中',
    'repairing': '维修中',
    'completed': '已完成',
    'afterSale': '售后/退款',
    'cancelled': '已取消',
    'deliveryFailed': '配送失败',
    'installFailed': '安装失败'
  }
  return statusMap[status] || '未知状态'
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 计算商品总数量
const getTotalQuantity = (items) => {
  return items.reduce((total, item) => total + item.quantity, 0)
}

// 跳转到订单详情
const goToDetail = (orderId) => {
  uni.navigateTo({
    url: `/pages/order/detail?id=${orderId}`
  })
}

// 取消订单
const cancelOrder = (orderId) => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消这个订单吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '订单已取消',
          icon: 'success'
        })
        // 实际应用中调用API取消订单
        loadOrderList()
      }
    }
  })
}

// 支付订单
const payOrder = (orderId) => {
  uni.showToast({
    title: '跳转支付页面',
    icon: 'none'
  })
}

// 查看物流
const trackDelivery = (orderId) => {
  uni.navigateTo({
    url: `/pages/order/detail?id=${orderId}`
  })
}

// 联系安装师傅
const contactInstaller = (orderId) => {
  uni.showToast({
    title: '联系安装师傅',
    icon: 'none'
  })
}

// 联系维修师傅
const contactRepairman = (orderId) => {
  uni.showToast({
    title: '联系维修师傅',
    icon: 'none'
  })
}

// 再次下单
const reorder = (orderId) => {
  uni.showToast({
    title: '再次下单',
    icon: 'none'
  })
}

// 查看售后详情
const viewAfterSale = (orderId) => {
  uni.showToast({
    title: '查看售后详情',
    icon: 'none'
  })
}


</script>

<style scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background-color: #fff;
  padding: 20rpx;
  position: sticky;
  top: 0;
  z-index: 10;
  overflow-x: auto;
  white-space: nowrap;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex-shrink: 0;
  text-align: center;
  padding: 16rpx 24rpx;
  margin: 0 8rpx;
  font-size: 26rpx;
  color: #666;
  position: relative;
  border-radius: 8rpx;
  background-color: transparent;
  transition: all 0.2s ease;
  min-width: 80rpx;
  font-weight: 400;
}

.tab-item.active {
  color: #2979ff;
  background-color: #f0f7ff;
  font-weight: 500;
}

.tab-item .badge {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  background-color: #ff4757;
  color: #fff;
  font-size: 18rpx;
  border-radius: 50%;
  min-width: 24rpx;
  height: 24rpx;
  line-height: 24rpx;
  text-align: center;
  font-weight: 500;
}

/* 订单列表 */
.order-list {
  padding: 20rpx;
}

.order-item {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
  border: 1rpx solid #f0f0f0;
}

.order-item:active {
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.08);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx;
  border-bottom: 1rpx solid #f8f9fa;
}

.order-info {
  flex: 1;
}

.order-info .order-no {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.order-info .order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  font-size: 22rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 4rpx;
  white-space: nowrap;
}

.status-pending {
  color: #ff9500;
  background-color: #fff5e6;
}

.status-processing {
  color: #52c41a;
  background-color: #f6ffed;
}

.status-installing {
  color: #1890ff;
  background-color: #e6f7ff;
}

.status-repairing {
  color: #722ed1;
  background-color: #f9f0ff;
}

.status-completed {
  color: #8c8c8c;
  background-color: #f5f5f5;
}

.status-afterSale {
  color: #ff4d4f;
  background-color: #fff2f0;
}

.status-deliveryFailed, .status-installFailed {
  color: #ff4d4f;
  background-color: #fff2f0;
}

/* 商品区域 */
.goods-section {
  padding: 0 24rpx 16rpx;
}

.goods-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.goods-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.goods-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  background-color: #f8f9fa;
}

.goods-info {
  flex: 1;
  min-width: 0;
}

.goods-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 6rpx;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.goods-spec {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.goods-quantity {
  font-size: 24rpx;
  color: #666;
}

.goods-price {
  font-size: 28rpx;
  color: #fa3534;
  font-weight: 600;
  text-align: right;
  min-width: 100rpx;
}

/* 订单底部 */
.order-footer {
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20rpx;
}

.total-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 200rpx;
}

.total-text {
  font-size: 26rpx;
  color: #666;
}

.total-price {
  font-size: 32rpx;
  color: #fa3534;
  font-weight: 700;
  margin-left: 8rpx;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.btn {
  padding: 10rpx 18rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  border: none;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.btn:active::before {
  width: 200%;
  height: 200%;
}

.btn-outline {
  background: rgba(255, 255, 255, 0.8);
  color: #666;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.btn-outline:active {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.btn-primary:active {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}
</style>
