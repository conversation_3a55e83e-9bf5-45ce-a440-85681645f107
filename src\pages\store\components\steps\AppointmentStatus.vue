<template>
  <view class="appointment-status">
    <!-- 预约信息卡片 -->
    <view class="form-section">
      <view class="section-title">
        <view class="title-icon">
          <text class="icon">📅</text>
        </view>
        <view>
          <text class="title-text">预约信息</text>
          <text class="description">安全指导和设备安装预约详情</text>
        </view>
      </view>

      <!-- 预约基本信息 -->
      <view class="appointment-info">
        <!-- 预约时间 - 突出显示 -->
        <view class="time-highlight">
          <view class="time-icon">📅</view>
          <view class="time-content">
            <view class="time-date">{{ appointment.date }}</view>
            <view class="time-slot">{{ appointment.timeSlot }}</view>
          </view>
          <view class="status-badge" :class="`status-${appointment.status}`">
            {{ getStatusText(appointment.status) }}
          </view>
        </view>

        <!-- 联系信息 -->
        <view class="contact-info">
          <view class="contact-item">
            <view class="contact-icon">👤</view>
            <view class="contact-content">
              <view class="contact-label">联系人</view>
              <view class="contact-value">{{ appointment.contactName }}</view>
            </view>
          </view>
          <view class="contact-item">
            <view class="contact-icon">📞</view>
            <view class="contact-content">
              <view class="contact-label">联系电话</view>
              <view class="contact-value">{{ appointment.contactPhone }}</view>
            </view>
          </view>
        </view>

        <!-- 预约编号 -->
        <view class="appointment-id">
          <view class="id-label">预约编号</view>
          <view class="id-value">{{ appointment.appointmentId || 'APT' + Date.now() }}</view>
        </view>

        <!-- 备注信息 -->
        <view v-if="appointment.remarks" class="remarks-section">
          <view class="remarks-label">备注信息</view>
          <view class="remarks-content">{{ appointment.remarks }}</view>
        </view>
      </view>

      <!-- 取消理由显示 -->
      <view v-if="appointment.status === 'cancelled' && appointment.cancelReason" class="cancel-reason-section">
        <view class="cancel-reason-label">取消理由</view>
        <view class="cancel-reason-content">{{ appointment.cancelReason }}</view>
        <view class="cancel-time" v-if="appointment.cancelledTime">
          取消时间：{{ formatTime(appointment.cancelledTime) }}
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="card-actions" v-if="appointment.status === 'confirmed'">
        <button class="btn btn-outline" @click="modifyAppointment">修改预约</button>
        <button class="btn btn-danger" @click="showCancelModal">取消预约</button>
      </view>

      <!-- 重新预约按钮 -->
      <view class="card-actions" v-if="appointment.status === 'cancelled'">
        <button class="btn btn-primary" @click="showRebookModal">重新预约</button>
      </view>
    </view>

    <!-- 安装进度卡片 -->
    <view class="form-section" v-if="appointment.status !== 'cancelled'">
      <view class="section-title">
        <view class="title-icon">
          <text class="icon">🔧</text>
        </view>
        <view>
          <text class="title-text">安装进度</text>
          <text class="description">安全指导和设备安装进度跟踪</text>
        </view>
      </view>

      <view class="progress-timeline">
        <view class="timeline-item" :class="{ 'completed': isStepCompleted('confirmed'), 'current': appointment.status === 'confirmed' }">
          <view class="timeline-indicator">
            <view class="timeline-dot"></view>
            <view class="timeline-line" v-if="!isLastStep('confirmed')"></view>
          </view>
          <view class="timeline-content">
            <view class="timeline-header">
              <view class="timeline-title">预约确认</view>
              <view class="timeline-time" v-if="appointment.confirmedTime">
                {{ formatTime(appointment.confirmedTime) }}
              </view>
            </view>
            <view class="timeline-desc">预约已确认，等待安全指导员联系</view>
          </view>
        </view>

        <view class="timeline-item" :class="{ 'completed': isStepCompleted('in-service'), 'current': appointment.status === 'in-service' }">
          <view class="timeline-indicator">
            <view class="timeline-dot"></view>
            <view class="timeline-line" v-if="!isLastStep('in-service')"></view>
          </view>
          <view class="timeline-content">
            <view class="timeline-header">
              <view class="timeline-title">上门服务</view>
              <view class="timeline-time" v-if="appointment.serviceStartTime">
                {{ formatTime(appointment.serviceStartTime) }}
              </view>
            </view>
            <view class="timeline-desc">安全指导员正在提供安全指导和设备安装服务</view>
            <view v-if="appointment.serviceWorker && appointment.status === 'in-service'" class="worker-info">
              <view class="worker-avatar">👨‍🔧</view>
              <view class="worker-details">
                <view class="worker-name">{{ appointment.serviceWorker.name }}</view>
                <view class="worker-phone">{{ appointment.serviceWorker.phone }}</view>
              </view>
            </view>
          </view>
        </view>

        <view class="timeline-item" :class="{ 'completed': isStepCompleted('completed'), 'current': appointment.status === 'completed' }">
          <view class="timeline-indicator">
            <view class="timeline-dot"></view>
          </view>
          <view class="timeline-content">
            <view class="timeline-header">
              <view class="timeline-title">服务完成</view>
              <view class="timeline-time" v-if="appointment.completedTime">
                {{ formatTime(appointment.completedTime) }}
              </view>
            </view>
            <view class="timeline-desc">安全指导和设备安装已完成，可正常使用</view>
          </view>
        </view>
      </view>


    </view>

    <!-- 取消预约弹窗 -->
    <view v-if="showCancelDialog" class="modal-overlay" @click="closeCancelModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <view class="modal-title">取消预约</view>
          <view class="modal-close" @click="closeCancelModal">×</view>
        </view>

        <view class="modal-body">
          <view class="form-item">
            <view class="form-label">取消理由 <text class="required">*</text></view>
            <textarea
              v-model="cancelForm.reason"
              class="form-textarea"
              placeholder="请填写取消预约的理由"
              maxlength="200"
              :show-count="true"
            />
          </view>
        </view>

        <view class="modal-footer">
          <button class="btn btn-outline" @click="closeCancelModal">取消</button>
          <button
            class="btn btn-danger"
            @click="confirmCancel"
            :disabled="!cancelForm.reason.trim()"
          >
            确认取消
          </button>
        </view>
      </view>
    </view>

    <!-- 修改预约确认弹窗 -->
    <view v-if="showModifyDialog" class="modal-overlay" @click="closeModifyModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <view class="modal-title">修改预约</view>
          <view class="modal-close" @click="closeModifyModal">×</view>
        </view>

        <view class="modal-body">
          <view class="confirm-content">
            <view class="confirm-icon">⚠️</view>
            <view class="confirm-text">
              <view class="confirm-title">确认修改预约信息？</view>
              <view class="confirm-desc">修改后需要重新填写预约信息，当前预约状态将被清除</view>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="btn btn-outline" @click="closeModifyModal">取消</button>
          <button class="btn btn-primary" @click="confirmModify">确认修改</button>
        </view>
      </view>
    </view>

    <!-- 重新预约确认弹窗 -->
    <view v-if="showRebookDialog" class="modal-overlay" @click="closeRebookModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <view class="modal-title">重新预约</view>
          <view class="modal-close" @click="closeRebookModal">×</view>
        </view>

        <view class="modal-body">
          <view class="confirm-content">
            <view class="confirm-icon">📅</view>
            <view class="confirm-text">
              <view class="confirm-title">开始重新预约？</view>
              <view class="confirm-desc">将清除当前取消的预约记录，重新填写预约信息</view>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="btn btn-outline" @click="closeRebookModal">取消</button>
          <button class="btn btn-primary" @click="confirmRebook">开始预约</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, ref, defineEmits } from 'vue'

// Props
const props = defineProps({
  appointment: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update-appointment', 'rebook'])

// 响应式数据
const showCancelDialog = ref(false)
const showModifyDialog = ref(false)
const showRebookDialog = ref(false)
const cancelForm = ref({
  reason: ''
})

// 方法
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待确认',
    'confirmed': '已确认',
    'in-service': '服务中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知状态'
}

const isStepCompleted = (step) => {
  const statusOrder = ['confirmed', 'in-service', 'completed']
  const currentIndex = statusOrder.indexOf(props.appointment.status)
  const stepIndex = statusOrder.indexOf(step)
  return stepIndex <= currentIndex
}

const isLastStep = (step) => {
  return step === 'completed'
}



const formatTime = (timeStr) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`
}

const modifyAppointment = () => {
  showModifyDialog.value = true
}

// 显示取消预约弹窗
const showCancelModal = () => {
  showCancelDialog.value = true
  cancelForm.value.reason = ''
}

// 关闭取消预约弹窗
const closeCancelModal = () => {
  showCancelDialog.value = false
  cancelForm.value.reason = ''
}

// 确认取消预约
const confirmCancel = () => {
  if (!cancelForm.value.reason.trim()) {
    uni.showToast({
      title: '请填写取消理由',
      icon: 'none'
    })
    return
  }

  uni.showLoading({
    title: '取消中...',
    mask: true
  })

  setTimeout(() => {
    uni.hideLoading()

    // 更新预约状态
    const updatedAppointment = {
      ...props.appointment,
      status: 'cancelled',
      cancelReason: cancelForm.value.reason,
      cancelledTime: new Date().toISOString()
    }

    emit('update-appointment', updatedAppointment)

    uni.showToast({
      title: '预约已取消',
      icon: 'success'
    })

    closeCancelModal()
  }, 1000)
}

// 显示修改预约弹窗
const showModifyModal = () => {
  showModifyDialog.value = true
}

// 关闭修改预约弹窗
const closeModifyModal = () => {
  showModifyDialog.value = false
}

// 确认修改预约
const confirmModify = () => {
  closeModifyModal()
  emit('rebook')
  uni.showToast({
    title: '请重新填写预约信息',
    icon: 'none'
  })
}

// 显示重新预约弹窗
const showRebookModal = () => {
  showRebookDialog.value = true
}

// 关闭重新预约弹窗
const closeRebookModal = () => {
  showRebookDialog.value = false
}

// 确认重新预约
const confirmRebook = () => {
  closeRebookModal()
  emit('rebook')
  uni.showToast({
    title: '请填写新的预约信息',
    icon: 'none'
  })
}
</script>

<style scoped>
.appointment-status {
  padding: 10rpx;
}

.form-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx 30rpx 50rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.title-icon {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.title-icon .icon {
  font-size: 28rpx;
  color: #fff;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.description {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

/* 预约信息样式 */
.appointment-info {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 时间突出显示 */
.time-highlight {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  color: white;
  min-height: 80rpx;
}

.time-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.time-content {
  flex: 1;
}

.time-date {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
  color: white;
}

.time-slot {
  font-size: 26rpx;
  opacity: 0.9;
  color: white;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 联系信息 */
.contact-info {
  display: flex;
  gap: 20rpx;
}

.contact-item {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.contact-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
}

.contact-content {
  flex: 1;
}

.contact-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.contact-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 预约编号 */
.appointment-id {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f0f2f5;
  border-radius: 12rpx;
}

.id-label {
  font-size: 26rpx;
  color: #666;
}

.id-value {
  font-size: 26rpx;
  color: #333;
  font-family: 'Courier New', monospace;
}

/* 备注信息 */
.remarks-section {
  padding: 20rpx;
  background-color: #fffbe6;
  border-radius: 12rpx;
  border-left: 4rpx solid #fadb14;
}

.remarks-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.remarks-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.card-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.btn-outline {
  background-color: #f5f5f5;
  color: #666;
}

.btn-danger {
  background-color: #ff4d4f;
  color: #fff;
}

.btn:active {
  opacity: 0.8;
}

/* 进度时间线样式 */
.progress-timeline {
  margin-bottom: 40rpx;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
  position: relative;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 24rpx;
  position: relative;
}

.timeline-dot {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background-color: #e8e8e8;
  border: 3rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.timeline-item.completed .timeline-dot {
  background-color: #52c41a;
}

.timeline-item.current .timeline-dot {
  background-color: #1890ff;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10rpx rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

.timeline-line {
  width: 2rpx;
  height: 60rpx;
  background-color: #e8e8e8;
  margin-top: 8rpx;
}

.timeline-item.completed .timeline-line {
  background-color: #52c41a;
}

.timeline-item.current .timeline-line {
  background-color: #1890ff;
}

.timeline-content {
  flex: 1;
  padding-top: 4rpx;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.timeline-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.timeline-item.current .timeline-title {
  color: #1890ff;
}

.timeline-item.completed .timeline-title {
  color: #52c41a;
}

.timeline-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.timeline-time {
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

/* 服务人员信息 */
.worker-info {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
  padding: 16rpx;
  background-color: #f0f9ff;
  border-radius: 12rpx;
  border: 1rpx solid #bae7ff;
}

.worker-avatar {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.worker-details {
  flex: 1;
}

.worker-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.worker-phone {
  font-size: 26rpx;
  color: #1890ff;
}

/* 取消理由显示 */
.cancel-reason-section {
  padding: 20rpx;
  background-color: #fff2f0;
  border-radius: 12rpx;
  border-left: 4rpx solid #ff4d4f;
  margin-top: 30rpx;
}

.cancel-reason-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.cancel-reason-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.cancel-time {
  font-size: 24rpx;
  color: #999;
}

.btn-primary {
  background-color: #1890ff;
  color: #fff;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.required {
  color: #ff4d4f;
}

.form-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #1890ff;
  outline: none;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-footer .btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

.modal-footer .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}



/* 确认弹窗内容样式 */
.confirm-content {
  display: flex;
  align-items: flex-start;
  padding: 20rpx 0;
}

.confirm-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
  margin-top: 8rpx;
}

.confirm-text {
  flex: 1;
}

.confirm-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.confirm-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
</style>
