<template>
  <view class="container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 安全审查详情 -->
    <view v-else class="content">
      <!-- 服务商信息 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>服务商信息</text>
        </view>
        <view class="info-card">
          <view class="info-row">
            <text class="info-label">服务商名称</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.name }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">联系人</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.contact }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">联系电话</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.phone }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">核查时间</text>
            <text class="info-value">{{ safetyCheckData.serviceProvider.checkTime }}</text>
          </view>
        </view>
      </view>

      <!-- 使用场景 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>使用场景</text>
        </view>
        <view class="image-section">
          <text class="scene-desc">{{ safetyCheckData.usageScene.description }}</text>
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.usageScene.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.usageScene.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 气瓶存放区 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>气瓶存放区</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.cylinderStorage.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.cylinderStorage.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 报警器安装 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>报警器安装</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.alarmInstallation.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.alarmInstallation.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 消防设备 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>消防设备</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.fireEquipment.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.fireEquipment.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 管道阀门安装图 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>管道阀门安装图</text>
        </view>
        <view class="image-section">
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.pipelineValve.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.pipelineValve.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 燃气设备使用图 -->
      <view class="section">
        <view class="section-title">
          <view class="section-title-bar"></view>
          <text>燃气设备使用图</text>
        </view>
        <view class="image-section">
          <text class="scene-desc">{{ safetyCheckData.gasEquipment.description }}</text>
          <view class="image-grid">
            <view 
              v-for="(image, index) in safetyCheckData.gasEquipment.images" 
              :key="index"
              class="image-item"
              @click="previewImage(image, safetyCheckData.gasEquipment.images)"
            >
              <image :src="image" mode="aspectFill" class="scene-image"></image>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const storeId = ref(null)
const checkId = ref(null)
const loading = ref(true)
const safetyCheckData = ref({})

// 页面加载时的处理
const onLoad = (options) => {
  storeId.value = options.storeId;
  checkId.value = options.checkId;
  loadSafetyCheckDetail();
}

// 加载安全审查详情数据
const loadSafetyCheckDetail = () => {
  loading.value = true;

  // 模拟API调用
  setTimeout(() => {
    // 模拟根据checkId获取对应的详情数据
    // 在实际项目中，这里应该根据checkId从API获取具体的详情数据
    const mockDetailData = {
      serviceProvider: {
        name: checkId.value === '1' ? "广州市燃气安全检测有限公司" :
              checkId.value === '2' ? "深圳市安全检测中心" : "东莞市燃气检测服务中心",
        contact: checkId.value === '1' ? "李工程师" :
                 checkId.value === '2' ? "王工程师" : "张工程师",
        phone: checkId.value === '1' ? "020-12345678" :
               checkId.value === '2' ? "0755-87654321" : "0769-88888888",
        checkTime: checkId.value === '1' ? "2023-10-15 14:30" :
                   checkId.value === '2' ? "2023-09-20 10:15" : "2023-08-10 16:20"
      },
      usageScene: {
        description: checkId.value === '1' ? "通风良好，有通风设备" :
                     checkId.value === '2' ? "室内通风一般，建议增加通风设备" : "通风设备完善，符合安全标准",
        images: [
          `https://via.placeholder.com/300x200/4a66b7/ffffff?text=通风设备${checkId.value}-1`,
          `https://via.placeholder.com/300x200/2979ff/ffffff?text=通风设备${checkId.value}-2`
        ]
      },
      cylinderStorage: {
        images: [
          `https://via.placeholder.com/300x200/34c759/ffffff?text=气瓶存放${checkId.value}-1`,
          `https://via.placeholder.com/300x200/ff9500/ffffff?text=气瓶存放${checkId.value}-2`,
          `https://via.placeholder.com/300x200/ff3b30/ffffff?text=气瓶存放${checkId.value}-3`
        ]
      },
      alarmInstallation: {
        images: [
          `https://via.placeholder.com/300x200/909399/ffffff?text=报警器${checkId.value}-1`,
          `https://via.placeholder.com/300x200/606266/ffffff?text=报警器${checkId.value}-2`
        ]
      },
      fireEquipment: {
        images: [
          `https://via.placeholder.com/300x200/e6a23c/ffffff?text=消防设备${checkId.value}-1`,
          `https://via.placeholder.com/300x200/f56c6c/ffffff?text=消防设备${checkId.value}-2`,
          `https://via.placeholder.com/300x200/67c23a/ffffff?text=消防设备${checkId.value}-3`
        ]
      },
      pipelineValve: {
        images: [
          `https://via.placeholder.com/300x200/409eff/ffffff?text=管道阀门${checkId.value}-1`,
          `https://via.placeholder.com/300x200/303133/ffffff?text=管道阀门${checkId.value}-2`
        ]
      },
      gasEquipment: {
        description: checkId.value === '1' ? "灶具有息火保护装置，热水器烟道是强排" :
                     checkId.value === '2' ? "燃气设备符合安全标准，定期维护良好" : "燃气设备安装规范，使用安全",
        images: [
          `https://via.placeholder.com/300x200/909399/ffffff?text=燃气灶具${checkId.value}`,
          `https://via.placeholder.com/300x200/606266/ffffff?text=热水器${checkId.value}`,
          `https://via.placeholder.com/300x200/303133/ffffff?text=烟道设备${checkId.value}`
        ]
      }
    };

    safetyCheckData.value = mockDetailData;
    loading.value = false;
  }, 1000);
}

// 预览图片
const previewImage = (currentImage, imageList) => {
  uni.previewImage({
    urls: imageList,
    current: currentImage
  });
}

// 直接加载数据
loadSafetyCheckDetail();

// 导出页面生命周期方法供uni-app使用
defineExpose({
  onLoad
})
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #4c6ef5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* 内容区域 */
.content {
  padding: 30rpx;
}

.section {
  margin-bottom: 30rpx;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
}

.section-title-bar {
  width: 6rpx;
  height: 30rpx;
  background-color: #4c6ef5;
  margin-right: 15rpx;
  border-radius: 3rpx;
}

.section-title text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 信息卡片 */
.info-card {
  background-color: #fff;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  width: 200rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  text-align: right;
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}

/* 图片区域 */
.image-section {
  background-color: #fff;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.scene-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 25rpx;
  line-height: 1.6;
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 10rpx;
  border-left: 4rpx solid #4c6ef5;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.image-item {
  height: 240rpx;
  border-radius: 10rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  background-color: #f5f5f5;
}

.image-item:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.12);
}

.scene-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
  object-fit: cover;
}

/* 响应式布局 */
@media (max-width: 600rpx) {
  .content {
    padding: 20rpx;
  }

  .section {
    margin-bottom: 25rpx;
  }

  .info-card, .image-section {
    padding: 25rpx;
  }

  .image-grid {
    gap: 15rpx;
  }

  .image-item {
    height: 220rpx;
  }
}
</style>
