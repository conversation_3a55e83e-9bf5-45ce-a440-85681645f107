<template>
  <view class="safety-step">
    <view class="safety-header">
      <view class="safety-title-container">
        <text class="safety-title">安全审查</text>
        <text class="safety-subtitle">请上传以下安全相关照片，用于安全审查</text>
      </view>
    </view>

    <view class="safety-form">
      <!-- 消防设施照片 -->
      <view class="form-section">
        <view class="form-section-header">
          <uni-icons type="fire" size="20" color="#ff7043"></uni-icons>
          <text class="section-title">消防设施照片</text>
        </view>
        <image-uploader 
          v-model="safetyImages.fire" 
          class="image-uploader-container"
          multiple 
          @delete="handleDeleteImage('fire', $event)" 
        />
      </view>
      
      <!-- 气瓶存放区照片 -->
      <view class="form-section">
        <view class="form-section-header">
          <uni-icons type="inbox" size="20" color="#42a5f5"></uni-icons>
          <text class="section-title">气瓶存放区照片</text>
        </view>
        <image-uploader 
          v-model="safetyImages.cylinder" 
          class="image-uploader-container"
          multiple 
          @delete="handleDeleteImage('cylinder', $event)" 
        />
      </view>
      
      <!-- 报警器安装照片 -->
      <view class="form-section">
        <view class="form-section-header">
          <uni-icons type="sound" size="20" color="#ffb300"></uni-icons>
          <text class="section-title">报警器安装照片</text>
        </view>
        <image-uploader 
          v-model="safetyImages.alarm" 
          class="image-uploader-container"
          multiple 
          @delete="handleDeleteImage('alarm', $event)" 
        />
      </view>
      
      <!-- 安全责任人管理 -->
      <view class="form-section manager-section">
        <view class="form-section-header">
          <uni-icons type="staff" size="20" color="#66bb6a"></uni-icons>
          <text class="section-title">安全责任人</text>
        </view>
        
        <!-- 安全责任人列表 -->
        <view class="safety-managers-container">
          <view v-for="(manager, index) in safetyManagers" :key="index" class="safety-manager-card">
            <view class="card-header">
              <view class="card-info">
                <text class="card-name">{{ manager.name }}</text>
                <text class="card-phone">{{ manager.phone }}</text>
              </view>
              <view class="card-actions">
                <view class="unlock-badge" :class="{'active': manager.canUnlock}">
                  <text class="badge-text">{{ manager.canUnlock ? '可开锁' : '不可开锁' }}</text>
                </view>
                <view class="icon-btn edit-icon-btn" @click="editManager(index)">
                  <uni-icons type="compose" size="24" color="#42a5f5"></uni-icons>
                </view>
                <view class="icon-btn delete-icon-btn" @click="removeManager(index)">
                  <uni-icons type="trash" size="24" color="#ff4d4f"></uni-icons>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 添加安全责任人按钮 -->
        <view class="add-manager-section">
          <button class="add-manager-btn" @click="openManagerDialog">
            <text class="btn-icon">+</text>
            <text class="btn-text">添加安全责任人</text>
          </button>
        </view>
      </view>
    </view>
    
    <!-- 安全责任人弹窗 -->
    <view v-if="showManagerDialog" class="manager-modal-mask" @tap="closeManagerDialog">
      <view class="manager-modal" @tap.stop="">
        <view class="modal-header">
          <text class="modal-title">{{ isEditing ? '编辑安全责任人' : '添加安全责任人' }}</text>
          <view class="modal-close" @click="closeManagerDialog">
            <text class="close-icon">✕</text>
          </view>
        </view>
        <view class="modal-body">
          <view class="input-group">
            <view class="input-item" :class="{'has-error': managerDialogErrors.name}">
              <view class="input-label">
                <text class="required">*</text>
                <text>姓名</text>
              </view>
              <input 
                class="input-field" 
                v-model.trim="newManager.name" 
                placeholder="请输入姓名"
                maxlength="6" 
                @input="validateManagerName"
                @blur="validateManagerName"
              />
              <text v-if="managerDialogErrors.name" class="error-msg">{{ managerDialogErrors.name }}</text>
            </view>
            
            <view class="input-item" :class="{'has-error': managerDialogErrors.phone}">
              <view class="input-label">
                <text class="required">*</text>
                <text>手机号码</text>
              </view>
              <input 
                class="input-field" 
                v-model.trim="newManager.phone" 
                placeholder="请输入手机号"
                maxlength="11"
                @input="validateManagerPhone"
                @blur="validateManagerPhone"
              />
              <text v-if="managerDialogErrors.phone" class="error-msg">{{ managerDialogErrors.phone }}</text>
            </view>
            
            <view class="switch-item">
              <view class="switch-label">
                <text>允许扫码开锁权限</text>
                <text class="switch-hint">开启后，该责任人可通过扫码开锁</text>
              </view>
              <switch 
                :checked="newManager.canUnlock" 
                @change="e => newManager.canUnlock = e.detail.value" 
                color="#4c6ef5" 
                class="permission-switch"
              />
            </view>
          </view>
        </view>
        <view class="modal-footer">
          <button class="btn btn-secondary" @click="closeManagerDialog">取消</button>
          <button class="btn btn-primary" @click="isEditing ? updateManager() : confirmAddManager()">
            {{ isEditing ? '确认更新' : '确认添加' }}
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import ImageUploader from '../ui/ImageUploader.vue'

export default {
  components: {
    ImageUploader,
    'uni-icons': () => import('@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue')
  },
  
  props: {
    modelValue: {
      type: Object,
      required: true
    }
  },
  
  data() {
    return {
      // 安全责任人相关
      safetyManagers: [],
      showManagerDialog: false,
      isEditing: false,
      editingIndex: -1,
      newManager: { name: '', phone: '', canUnlock: false },
      managerDialogErrors: { name: '', phone: '' },
      originalManager: null
    }
  },
  
  computed: {
    form: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    },
    safetyImages: {
      get() {
        return this.modelValue.safetyImages
      },
      set(value) {
        this.$emit('update:modelValue', {
          ...this.modelValue,
          safetyImages: value
        })
      }
    }
  },
  
  watch: {
    'modelValue.safetyManagers': {
      immediate: true,
      handler(newManagers) {
        if (Array.isArray(newManagers)) {
          this.safetyManagers = [...newManagers];
          // 确保每个安全责任人都有默认值
          this.safetyManagers.forEach(manager => {
            if (!manager.name) manager.name = '';
            if (!manager.phone) manager.phone = '';
            if (manager.canUnlock === undefined) manager.canUnlock = false;
          });
        }
      }
    }
  },
  
  created() {
    // 初始化安全责任人数组
    if (this.modelValue.safetyManagers && Array.isArray(this.modelValue.safetyManagers)) {
      this.safetyManagers = [...this.modelValue.safetyManagers];
    } else {
      this.safetyManagers = [];
      // 初始化表单中的安全责任人数组
      const updatedForm = { ...this.modelValue, safetyManagers: [] };
      this.$emit('update:modelValue', updatedForm);
    }
  },
  
  methods: {
    handleDeleteImage(category, index) {
      this.$emit('delete', category, index)
    },
    
    // 安全责任人相关方法
    openManagerDialog() {
      this.showManagerDialog = true;
      this.isEditing = false;
      this.editingIndex = -1;
      this.newManager = { name: '', phone: '', canUnlock: false };
      this.managerDialogErrors = { name: '', phone: '' };
      this.originalManager = null;
    },
    
    editManager(index) {
      const manager = this.safetyManagers[index];
      this.isEditing = true;
      this.editingIndex = index;
      this.newManager = {
        name: manager.name,
        phone: manager.phone,
        canUnlock: manager.canUnlock
      };
      this.originalManager = { ...manager }; // 保存原始数据用于取消时恢复
      this.managerDialogErrors = { name: '', phone: '' };
      this.showManagerDialog = true;
    },
    
    closeManagerDialog() {
      this.showManagerDialog = false;
      this.isEditing = false;
      this.editingIndex = -1;
      this.newManager = { name: '', phone: '', canUnlock: false };
      this.managerDialogErrors = { name: '', phone: '' };
      this.originalManager = null;
    },
    
    validateManagerName() {
      if (!this.newManager.name) {
        this.managerDialogErrors.name = '请输入姓名';
      } else if (this.newManager.name.length > 6) {
        this.managerDialogErrors.name = '姓名不能超过6个字符';
      } else {
        this.managerDialogErrors.name = '';
      }
    },
    
    validateManagerPhone() {
      const mobileReg = /^1[3-9]\d{9}$/;
      
      if (!this.newManager.phone) {
        this.managerDialogErrors.phone = '请输入手机号';
      } else if (!mobileReg.test(this.newManager.phone)) {
        this.managerDialogErrors.phone = '请输入有效的11位手机号';
      } else {
        // 检查重复手机号（编辑时排除当前手机号）
        const duplicateIndex = this.safetyManagers.findIndex((manager, index) => 
          manager.phone === this.newManager.phone && 
          (!this.isEditing || index !== this.editingIndex)
        );
        
        if (duplicateIndex !== -1) {
          this.managerDialogErrors.phone = '该手机号已存在';
        } else {
          this.managerDialogErrors.phone = '';
        }
      }
    },
    
    validateManagerForm() {
      let valid = true;
      
      // 姓名验证
      if (!this.newManager.name) {
        this.managerDialogErrors.name = '请输入姓名';
        valid = false;
      } else if (this.newManager.name.length > 6) {
        this.managerDialogErrors.name = '姓名不能超过6个字符';
        valid = false;
      } else {
        this.managerDialogErrors.name = '';
      }
      
      // 手机号验证
      const mobileReg = /^1[3-9]\d{9}$/;
      if (!this.newManager.phone) {
        this.managerDialogErrors.phone = '请输入手机号';
        valid = false;
      } else if (!mobileReg.test(this.newManager.phone)) {
        this.managerDialogErrors.phone = '请输入有效的11位手机号';
        valid = false;
      } else {
        // 检查重复手机号（编辑时排除当前手机号）
        const duplicateIndex = this.safetyManagers.findIndex((manager, index) => 
          manager.phone === this.newManager.phone && 
          (!this.isEditing || index !== this.editingIndex)
        );
        
        if (duplicateIndex !== -1) {
          this.managerDialogErrors.phone = '该手机号已存在';
          valid = false;
        } else {
          this.managerDialogErrors.phone = '';
        }
      }
      
      return valid;
    },
    
    confirmAddManager() {
      // 校验
      if (!this.validateManagerForm()) return;
      // 添加
      this.safetyManagers.push({ ...this.newManager });
      const updatedForm = { ...this.form, safetyManagers: this.safetyManagers };
      this.$emit('update:modelValue', updatedForm);
      this.closeManagerDialog();
    },
    
    updateManager() {
      // 校验
      if (!this.validateManagerForm()) return;
      
      // 更新
      this.$set(this.safetyManagers, this.editingIndex, { ...this.newManager });
      const updatedForm = { ...this.form, safetyManagers: this.safetyManagers };
      this.$emit('update:modelValue', updatedForm);
      this.closeManagerDialog();
    },
    
    removeManager(index) {
      this.safetyManagers.splice(index, 1);
      
      // 更新表单数据
      const updatedForm = { ...this.form, safetyManagers: this.safetyManagers };
      this.$emit('update:modelValue', updatedForm);
    },
    
    updateManagerUnlock(index, value) {
      this.safetyManagers[index].canUnlock = value;
      
      // 更新表单数据
      const updatedForm = { ...this.form, safetyManagers: this.safetyManagers };
      this.$emit('update:modelValue', updatedForm);
    }
  }
}
</script>

<style lang="scss" scoped>
.safety-step {
  padding: 20rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* Header styling */
.safety-header {
  margin-bottom: 30rpx;
  padding: 30rpx 0;
  position: relative;
}

.safety-title-container {
  padding: 0 20rpx;
}

.safety-title {
  font-size: 44rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.safety-subtitle {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  display: block;
}

/* Form section styling */
.safety-form {
  padding: 0 10rpx;
}

.form-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-left: 10rpx;
}

/* Image uploader styling */
.image-uploader-container {
  margin-top: 10rpx;
}

:deep(.uni-file-picker__container) {
  margin-top: 20rpx;
}

:deep(.file-picker__box) {
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
}

/* Input field styling */
.input-group {
  margin-bottom: 24rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
}

.required {
  color: #f56c6c;
  margin-right: 6rpx;
  font-weight: bold;
}

.input-field {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  background-color: #f9f9f9;
  color: #333;
  transition: border-color 0.3s, background-color 0.3s;
}

.input-field:focus {
  border-color: #3c9cff;
  background-color: rgba(60, 156, 255, 0.05);
}

.input-field::placeholder {
  color: #bbb;
}

/* 安全责任人样式 */
.manager-section {
  border-top: 6rpx solid rgba(102, 187, 106, 0.2);
}

.safety-managers-container {
  margin-top: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.safety-manager-card {
  background: #fff;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.safety-manager-card:hover {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transform: translateY(-1rpx);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.card-info {
  flex: 1;
  min-width: 0;
}

.card-name {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.card-phone {
  display: block;
  font-size: 26rpx;
  color: #666;
}

.card-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-shrink: 0;
}

.unlock-badge {
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  background: #f5f5f5;
  color: #999;
}

.unlock-badge.active {
  background: #e6f7ff;
  color: var(--primary-color, #42a5f5);
}

.badge-text {
  font-weight: 500;
}

.icon-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: none;
  background: transparent;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  transition: all 0.2s ease;
  flex-shrink: 0;
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.icon-btn uni-icons {
  display: block;
  line-height: 1;
}

.edit-icon-btn {
  color: var(--primary-color, #42a5f5);
}

.edit-icon-btn:hover {
  background: rgba(66, 165, 245, 0.1);
}

.delete-icon-btn {
  color: #ff4d4f;
}

.delete-icon-btn:hover {
  background: rgba(255, 77, 79, 0.1);
}

.add-manager-section {
  margin-top: 24rpx;
}

.add-manager-btn {
  width: 100%;
  height: 88rpx;
  background: transparent;
  border: 2rpx dashed #d9d9d9;
  border-radius: 8rpx;
  color: var(--primary-color, #42a5f5);
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.2s ease;
}

.add-manager-btn:hover {
  border-color: var(--primary-color, #42a5f5);
  background: rgba(66, 165, 245, 0.04);
}

.btn-icon {
  font-size: 32rpx;
  font-weight: 600;
}

.btn-text {
  font-weight: 500;
}

/* 弹窗样式 */
.manager-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.45);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.manager-modal {
  background: #fff;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  animation: modalFadeIn 0.2s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #262626;
}

.modal-close {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-icon {
  font-size: 24rpx;
  color: #999;
}

.modal-body {
  padding: 32rpx;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.input-item {
  position: relative;
  margin-bottom: 20rpx;
}

.input-item.has-error .input-field {
  border-color: #ff4d4f;
}

.error-msg {
  position: absolute;
  left: 0;
  top: 100%;
  font-size: 22rpx;
  color: #ff4d4f;
  margin-top: 4rpx;
}

.switch-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
}

.switch-label {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.switch-label text:first-child {
  font-size: 28rpx;
  color: #595959;
  font-weight: 500;
}

.switch-hint {
  font-size: 24rpx;
  color: #8c8c8c;
}

.permission-switch {
  transform: scale(0.9);
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16rpx;
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.btn {
  height: 72rpx;
  line-height: 72rpx;
  padding: 0 32rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary {
  flex: 1;
  background: #f5f5f5;
  color: #666;
}

.btn-secondary:hover {
  background: #e6e6e6;
}

.btn-primary {
  flex: 1;
  background: var(--primary-color, #42a5f5);
  color: #fff;
}

.btn-primary:hover {
  background: #1890ff;
}

@media screen and (max-width: 768rpx) {
  .card-header {
    flex-wrap: wrap;
    gap: 12rpx;
  }
  
  .card-actions {
    width: 100%;
    justify-content: flex-end;
    margin-top: 12rpx;
  }
  
  .manager-modal {
    margin: 20rpx;
    max-width: none;
  }
  
  .modal-body {
    padding: 24rpx;
  }
  
  .modal-footer {
    padding: 16rpx 24rpx 24rpx;
  }
}
</style> 