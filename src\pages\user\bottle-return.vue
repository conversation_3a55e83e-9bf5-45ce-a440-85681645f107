<template>
  <view class="container">
    <view class="page-header">
      <view class="title">退瓶确认</view>
      <view class="subtitle">请确认以下信息无误</view>
    </view>

    <view class="loading-container" v-if="loading">
      <uni-icons type="spinner-cycle" size="40" color="#2979ff"></uni-icons>
      <text class="loading-text">正在加载信息...</text>
    </view>

    <view class="error-container" v-if="error">
      <uni-icons type="error" size="80" color="#fa3534"></uni-icons>
      <text class="error-text">{{ errorMsg }}</text>
      <button class="action-button primary" @click="scanAgain">重新扫码</button>
    </view>

    <view class="info-container" v-if="!loading && !error && deviceInfo">
      <view class="device-type-badge" :class="deviceInfo.type === 'cylinder' ? 'cylinder' : 'alarm'">
        <uni-icons :type="deviceInfo.type === 'cylinder' ? 'download' : 'notification'" size="18" color="#ffffff"></uni-icons>
        <text>{{ deviceInfo.type === 'cylinder' ? '气瓶' : '报警器' }}</text>
      </view>
      
      <!-- 设备基本信息卡片 -->
      <view class="card">
        <view class="card-title">设备信息</view>
        
        <view class="info-grid">
          <view class="info-item">
            <text class="label">设备编号</text>
            <text class="value">{{ deviceInfo.id }}</text>
          </view>
          
          <view class="info-item">
            <text class="label">设备类型</text>
            <text class="value">{{ deviceInfo.modelName }}</text>
          </view>
          
          <view class="info-item" v-if="deviceInfo.type === 'cylinder'">
            <text class="label">规格</text>
            <text class="value highlight">{{ deviceInfo.specification }}</text>
          </view>
          
          <view class="info-item">
            <text class="label">状态</text>
            <view class="status-tag" :class="deviceInfo.status === '正常' ? 'normal' : 'warning'">
              {{ deviceInfo.status }}
            </view>
          </view>
        </view>
      </view>

      <!-- 租借信息卡片 -->
      <view class="card">
        <view class="card-title">租借信息</view>
        
        <view class="info-grid">
          <view class="info-item">
            <text class="label">开始日期</text>
            <text class="value">{{ deviceInfo.borrowDate }}</text>
          </view>
          
          <view class="info-item">
            <text class="label">已使用</text>
            <text class="value highlight">{{ deviceInfo.borrowDays }}天</text>
          </view>
          
          <view class="info-item">
            <text class="label">计费方式</text>
            <text class="value">{{ deviceInfo.type === 'cylinder' ? '月租+日租' : '日租' }}</text>
          </view>
          
          <view class="info-item">
            <text class="label">今日归还</text>
            <text class="value">{{ formatDate(new Date()) }}</text>
          </view>
        </view>
      </view>
      
      <!-- 费用结算卡片 -->
      <view class="card" v-if="deviceInfo.type === 'cylinder'">
        <view class="card-title">费用结算</view>
        
        <view class="fee-table">
          <view class="fee-row gas-fee-info">
            <text class="fee-label">燃气费用</text>
            <text class="fee-amount non-refundable">¥{{ deviceInfo.gasAmount.toFixed(2) }} (不可退)</text>
          </view>

          <view class="fee-row">
            <text class="fee-label">押金金额</text>
            <text class="fee-amount positive">+¥{{ deviceInfo.deposit.toFixed(2) }}</text>
          </view>

          <view class="fee-row" v-if="deviceInfo.rentalInfo.baseMonthFee > 0">
            <text class="fee-label">基础月租 ({{ deviceInfo.rentalInfo.fullMonths }}个月)</text>
            <text class="fee-amount">-¥{{ deviceInfo.rentalInfo.baseMonthFee.toFixed(2) }}</text>
          </view>

          <view class="fee-row" v-if="deviceInfo.rentalInfo.extraDaysFee > 0">
            <text class="fee-label">额外天数 ({{ deviceInfo.rentalInfo.extraDays }}天)</text>
            <text class="fee-amount">-¥{{ deviceInfo.rentalInfo.extraDaysFee.toFixed(2) }}</text>
          </view>

          <view class="divider"></view>

          <view class="fee-row total">
            <text class="fee-label">应退金额（仅押金）</text>
            <text class="fee-amount" :class="deviceInfo.rentalInfo.refundAmount >= 0 ? 'positive' : ''">
              {{ deviceInfo.rentalInfo.refundAmount >= 0 ? '+' : '' }}¥{{ deviceInfo.rentalInfo.refundAmount.toFixed(2) }}
            </text>
          </view>
        </view>
        
        <view class="note" v-if="deviceInfo.status !== '正常'">
          <uni-icons type="warning-filled" size="16" color="#fa3534"></uni-icons>
          <text>设备状态异常，可能需额外检查并产生维修费用</text>
        </view>
      </view>
      
      <!-- 报警器结算卡片 -->
      <view class="card" v-else>
        <view class="card-title">费用结算</view>
        
        <view class="fee-table">
          <view class="fee-row" v-if="deviceInfo.deposit > 0">
            <text class="fee-label">押金金额</text>
            <text class="fee-amount positive">+¥{{ deviceInfo.deposit.toFixed(2) }}</text>
          </view>
          
          <view class="fee-row" v-if="deviceInfo.rentalInfo.totalFee > 0">
            <text class="fee-label">租金 ({{ deviceInfo.borrowDays }}天)</text>
            <text class="fee-amount">-¥{{ deviceInfo.rentalInfo.totalFee.toFixed(2) }}</text>
          </view>
          
          <view class="divider" v-if="deviceInfo.deposit > 0"></view>
          
          <view class="fee-row total" v-if="deviceInfo.deposit > 0">
            <text class="fee-label">应退金额</text>
            <text class="fee-amount" :class="deviceInfo.rentalInfo.refundAmount >= 0 ? 'positive' : ''">
              {{ deviceInfo.rentalInfo.refundAmount >= 0 ? '+' : '' }}¥{{ deviceInfo.rentalInfo.refundAmount.toFixed(2) }}
            </text>
          </view>
        </view>
        
        <view class="note" v-if="deviceInfo.status !== '正常'">
          <uni-icons type="warning-filled" size="16" color="#fa3534"></uni-icons>
          <text>设备状态异常，可能需额外检查并产生维修费用</text>
        </view>
      </view>
      
      <view class="info-note">
        <text>• 燃气费用为消费性支出，不可退还</text>
      </view>
      <view class="info-note">
        <text>• 押金将在确认设备无损后的1-3个工作日内退回您的账户</text>
      </view>
      
      <view class="action-buttons">
        <button class="action-button cancel" @click="cancel">取消</button>
        <button class="action-button primary" @click="confirmReturn">确认归还</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      code: '',
      loading: true,
      error: false,
      errorMsg: '',
      deviceInfo: null
    }
  },

  onLoad(options) {
    if (options.code) {
      this.code = decodeURIComponent(options.code)
      this.fetchDeviceInfo()
    } else {
      this.error = true
      this.loading = false
      this.errorMsg = '未获取到设备编码，请重新扫码'
    }
  },

  methods: {
    // 获取设备信息
    fetchDeviceInfo() {
      this.loading = true;
      this.error = false;

      // 模拟API请求，实际应用中应该调用后端接口
      setTimeout(() => {
        // 根据扫码结果模拟不同设备类型的返回
        if (this.code.includes('CYL')) {
          // 气瓶信息 - 随机选择一种气瓶规格
          const specifications = ['5kg', '15kg', '50kg'];
          const spec = specifications[Math.floor(Math.random() * specifications.length)];
          // 使用与订单页面一致的押金价格
          const deposits = {'5kg': 20, '15kg': 30, '50kg': 60};
          const gasPrice = {'5kg': 50, '15kg': 100, '50kg': 320}; // 燃气价格（不可退）
          const monthlyRates = {'5kg': 10, '15kg': 20, '50kg': 30};
          const dailyRates = {'5kg': 0.5, '15kg': 0.8, '50kg': 1.2};

          // 生成租借天数
          const borrowDays = Math.floor(Math.random() * 90) + 1;
          const deposit = deposits[spec];
          const gasAmount = gasPrice[spec]; // 燃气费用（不可退）

          // 计算租金（从押金中扣除，不涉及燃气费用）
          const fullMonths = Math.floor(borrowDays / 30);
          const extraDays = borrowDays % 30;
          const baseMonthFee = fullMonths * monthlyRates[spec];
          const extraDaysFee = extraDays * dailyRates[spec];

          // 计算应退金额（只能退押金，扣除租金）
          const totalFee = baseMonthFee + extraDaysFee;
          const refundAmount = deposit - totalFee;
          
          this.deviceInfo = {
            type: 'cylinder',
            id: this.code,
            modelName: '液化石油气钢瓶',
            specification: spec,
            borrowDate: this.formatDate(this.randomDate(90)),
            borrowDays: borrowDays,
            deposit: deposit,
            gasAmount: gasAmount, // 燃气费用（不可退）
            status: Math.random() > 0.2 ? '正常' : '需检查',
            rentalInfo: {
              fullMonths: fullMonths,
              extraDays: extraDays,
              baseMonthFee: baseMonthFee,
              extraDaysFee: extraDaysFee,
              totalFee: totalFee,
              refundAmount: refundAmount
            }
          };
        } else if (this.code.includes('ALM')) {
          // 报警器信息
          const alarmTypes = ['家用燃气报警器', '商用燃气报警器', '便携式报警器'];
          const dailyRate = Math.random() > 0.5 ? 0 : 0.3;
          
          // 生成借用天数和押金
          const borrowDays = Math.floor(Math.random() * 365) + 1;
          const deposit = Math.random() > 0.5 ? 0 : 50;
          
          // 计算租金和退款金额
          const totalFee = borrowDays * dailyRate;
          const refundAmount = deposit - totalFee;
          
          this.deviceInfo = {
            type: 'alarm',
            id: this.code,
            modelName: alarmTypes[Math.floor(Math.random() * alarmTypes.length)],
            borrowDate: this.formatDate(this.randomDate(180)),
            borrowDays: borrowDays,
            deposit: deposit,
            status: Math.random() > 0.1 ? '正常' : '需维修',
            rentalInfo: {
              totalFee: totalFee,
              refundAmount: refundAmount
            }
          };
        } else {
          // 无效的二维码
          this.error = true;
          this.errorMsg = '无效的设备二维码，请联系客服';
        }

        this.loading = false;
      }, 1500);
    },

    // 确认归还
    confirmReturn() {
      // 显示确认对话框
      uni.showModal({
        title: '确认归还',
        content: `您确定要归还该${this.deviceInfo.type === 'cylinder' ? '气瓶' : '报警器'}吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '正在处理'
            });

            // 模拟API请求
            setTimeout(() => {
              uni.hideLoading();
              
              // 模拟结果，90%成功率
              if (Math.random() < 0.9) {
                // 成功
                let message = '归还成功';
                
                // 显示简短成功信息
                uni.showToast({
                  title: message,
                  icon: 'success',
                  duration: 2000
                });

                // 返回上一页
                setTimeout(() => {
                  uni.navigateBack();
                }, 2000);
              } else {
                // 失败，显示错误
                uni.showModal({
                  title: '归还失败',
                  content: '系统处理异常，请稍后重试或联系客服',
                  showCancel: false
                });
              }
            }, 2000);
          }
        }
      });
    },

    // 取消归还
    cancel() {
      uni.navigateBack();
    },

    // 重新扫码
    scanAgain() {
      uni.scanCode({
        success: (res) => {
          this.code = res.result;
          this.fetchDeviceInfo();
        },
        fail: () => {
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          });
        }
      });
    },

    // 生成随机日期（最近n天内）
    randomDate(days) {
      const today = new Date();
      const pastDate = new Date();
      pastDate.setDate(today.getDate() - Math.floor(Math.random() * days));
      return pastDate;
    },

    // 格式化日期为YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  }
}
</script>

<style scoped>
.container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-top: 10rpx;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-text, .error-text {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.error-text {
  margin-bottom: 30rpx;
}

.info-container {
  position: relative;
}

.device-type-badge {
  position: absolute;
  top: -15rpx;
  right: 30rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: white;
  z-index: 1;
  display: flex;
  align-items: center;
}

.device-type-badge text {
  margin-left: 6rpx;
}

.device-type-badge.cylinder {
  background-color: #2979ff;
}

.device-type-badge.alarm {
  background-color: #fa3534;
}

.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.info-grid {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  width: 50%;
  margin-bottom: 20rpx;
}

.label {
  font-size: 26rpx;
  color: #999;
  display: block;
  margin-bottom: 8rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.highlight {
  color: #2979ff;
  font-weight: bold;
}

.status-tag {
  display: inline-block;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.status-tag.normal {
  background-color: #19be6b;
}

.status-tag.warning {
  background-color: #fa3534;
}

.fee-table {
  width: 100%;
}

.fee-row {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
  font-size: 28rpx;
}

.fee-label {
  color: #666;
}

.fee-amount {
  font-weight: 500;
  color: #333;
}

.fee-amount.positive {
  color: #19be6b;
}

.fee-amount.non-refundable {
  color: #999;
  font-style: italic;
}

.gas-fee-info {
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 10rpx;
}

.divider {
  height: 1rpx;
  background-color: #eee;
  margin: 10rpx 0;
}

.fee-row.total {
  font-weight: bold;
  font-size: 32rpx;
}

.note {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  background-color: rgba(250, 53, 52, 0.1);
  padding: 16rpx;
  border-radius: 8rpx;
}

.note text {
  font-size: 24rpx;
  color: #fa3534;
  margin-left: 10rpx;
}

.info-note {
  background-color: #fff9e6;
  border-radius: 12rpx;
  padding: 20rpx;
  margin: 30rpx 0;
  font-size: 26rpx;
  color: #ff9900;
  text-align: center;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.action-button {
  width: 45%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  text-align: center;
  font-weight: 500;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.action-button.primary {
  background-color: #2979ff;
  color: white;
}

.action-button.cancel {
  background-color: #f5f5f5;
  color: #666;
}
</style> 