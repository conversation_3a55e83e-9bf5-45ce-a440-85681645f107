# 安全环境检查功能说明

## 功能概述

在维保员到达现场后，增加了一个"安全环境检查"步骤，用于确认现场安全环境是否合格。

## 功能特点

### 1. 检查状态
- **合格状态**: 现场安全环境符合作业要求
- **不合格状态**: 现场存在安全隐患，需要处理后才能继续作业

### 2. 问题记录
- 当检查不合格时，可以记录具体的问题描述
- 支持多个问题项的记录
- 问题描述清晰明确，便于后续处理

### 3. 现场照片
- 支持上传现场照片作为检查凭证
- 照片可以点击预览放大查看
- 支持多张照片展示

## 数据结构

```javascript
{
  name: '安全环境检查',
  completed: true,
  current: false,
  time: '2024-12-02T16:15:00.000Z',
  description: '现场安全环境检查合格/不合格',
  worker: '李师傅',
  safetyCheck: {
    status: 'qualified', // 'qualified': 合格, 'unqualified': 不合格
    issues: [            // 问题描述数组（不合格时使用）
      '燃气管道老化，存在漏气风险',
      '通风不良，空气流通不畅',
      '周围有易燃物品堆放'
    ],
    images: [            // 现场照片数组
      '/static/images/safety-check-1.jpg',
      '/static/images/safety-check-2.jpg'
    ]
  }
}
```

## 界面展示

### 合格状态
- 显示绿色的 ✓ 图标
- 状态文字为"安全环境合格"
- 不显示问题列表
- 显示现场照片

### 不合格状态
- 显示橙色的 ⚠ 图标
- 状态文字为"安全环境不合格"
- 显示具体问题列表
- 显示现场照片

## 演示功能

页面右上角有一个演示按钮，可以切换安全检查状态，方便查看不同状态下的界面效果。

## 使用场景

1. **维保员到达现场**: 首先进行安全环境检查
2. **检查合格**: 可以继续后续的安装/维修作业
3. **检查不合格**: 需要先处理安全隐患，记录问题并拍照，等待问题解决后再继续作业

## 技术实现

- 使用 Vue 3 Composition API
- 响应式数据绑定
- 条件渲染显示不同状态
- 图片预览功能
- 移动端适配的样式设计
