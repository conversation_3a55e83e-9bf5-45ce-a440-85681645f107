<template>
  <view class="container">
    <view class="header">
      <text class="title">我的租户</text>
      <view class="add-btn" @click="addStore">
        <text>新增租户</text>
      </view>
    </view>

    <view v-if="storeList.length === 0" class="empty-state">
      <image src="/static/images/default.png" mode="aspectFit"></image>
      <text class="empty-text">您还没有添加租户</text>
      <button class="primary-btn" @click="addStore">添加租户</button>
    </view>
    
    <view v-else class="store-list">
      <view 
        class="store-card"
        v-for="(store, index) in storeList" 
        :key="index"
      >
        <view class="store-card-top">
          <image 
            class="store-img" 
            :src="store.image || '/static/images/store-default.png'" 
            mode="aspectFill"
          ></image>
          
          <view class="store-info">
            <view class="store-name-container">
              <text class="store-name">{{store.name}}</text>
              <text v-if="store.isMain" class="main-tag">总店</text>
            </view>
            <view class="store-address">{{store.address}}</view>
            <view class="store-meta">
              <text class="store-id">ID: {{store.id}}</text>
            </view>
          </view>
        </view>
        
        <view class="store-card-body">
          <view class="store-data">
            <view class="data-item">
              <view class="data-label">报警器</view>
              <view class="data-value">{{store.alarmCount || 0}}</view>
            </view>
            
            <view class="data-item">
              <view class="data-label">气瓶</view>
              <view class="data-value">{{store.cylinderCount || 0}}</view>
            </view>
            
            <view class="data-item">
              <view class="data-label">合同</view>
              <view :class="['data-tag', `status-${store.contractStatus}`]">
                {{getStatusText(store.contractStatus)}}
              </view>
            </view>
            
            <view class="data-item">
              <view class="data-label">保险</view>
              <view :class="['data-tag', `status-${store.insuranceStatus}`]">
                {{getStatusText(store.insuranceStatus)}}
              </view>
            </view>
          </view>
          
          <view class="store-safety">
            <view class="safety-label">预约状态</view>
            <view :class="['safety-status', `safety-${store.appointmentStatus}`]">
              {{getAppointmentStatusText(store.appointmentStatus)}}
            </view>
          </view>
        </view>
        
        <view class="store-card-actions">
          <button class="action-btn outline" @click="viewStoreDetail(store)">查看详情</button>
          <button class="action-btn" @click="manageStore(store)">管理租户</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const storeList = ref([])

// 生命周期钩子
onMounted(() => {
  loadStoreList()
})

// 页面显示时重新加载数据
const onShow = () => {
  loadStoreList()
}

// 加载租户列表
const loadStoreList = () => {
  uni.showLoading({
    title: '加载中'
  })

  setTimeout(() => {
    storeList.value = [
      {
        id: 1,
        name: '燃气经营部(总店)',
        address: '广州市天河区珠江新城88号',
        image: 'https://ts3.tc.mm.bing.net/th/id/OIP-C.P-mROslgWTHtZEoWdbNzCQHaFj?rs=1&pid=ImgDetMain&o=7&rm=3',
        contractStatus: 'passed',
        alarmCount: 12,
        cylinderCount: 50,
        insuranceStatus: 'passed',
        appointmentStatus: 'confirmed',
        isMain: true
      },
      {
        id: 2,
        name: '燃气经营部(分店)',
        address: '广州市海珠区滨江东路120号',
        image: 'https://ts3.tc.mm.bing.net/th/id/OIP-C.P-mROslgWTHtZEoWdbNzCQHaFj?rs=1&pid=ImgDetMain&o=7&rm=3',
        contractStatus: 'processing',
        alarmCount: 5,
        cylinderCount: 23,
        insuranceStatus: 'pending',
        appointmentStatus: 'pending',
        isMain: false
      }
    ]

    uni.hideLoading()
  }, 500)
}

// 新增租户
const addStore = () => {
  uni.navigateTo({
    url: '/pages/store/add-store'
  })
}

// 查看租户详情
const viewStoreDetail = (store) => {
  uni.navigateTo({
    url: `/pages/store/detail?id=${store.id}`
  })
}

// 管理租户
const manageStore = (store) => {
  // 保存当前租户
  uni.setStorageSync('currentStoreId', store.id)

  uni.navigateTo({
    url: `/pages/store/manage?id=${store.id}`
  })
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '未提交',
    'processing': '审核中',
    'passed': '已通过',
    'failed': '未通过'
  }
  return statusMap[status] || '未提交'
}

// 获取状态样式类
const getStatusClass = (status) => {
  return `status-${status}`
}

// 获取预约状态文本
const getAppointmentStatusText = (status) => {
  const statusMap = {
    'pending': '未预约',
    'confirmed': '已预约',
    'in-service': '服务中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未预约'
}

// 导出页面生命周期方法供uni-app使用
defineExpose({
  onShow
})
</script>

<style scoped>
page {
  background-color: #f7f8fa;
}

.container {
  padding: 30rpx;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.add-btn {
  font-size: 28rpx;
  color: #2979ff;
  background-color: rgba(41, 121, 255, 0.1);
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.primary-btn {
  background-color: #2979ff;
  color: #fff;
  font-size: 28rpx;
  border-radius: 8rpx;
  height: 80rpx;
  line-height: 80rpx;
  width: 200rpx;
}

.primary-btn::after {
  border: none;
}

/* 租户列表 */
.store-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 租户卡片 */
.store-card {
  background-color: #fff;
  border-radius: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.store-card-top {
  display: flex;
  padding: 30rpx;
  background-color: #fff;
  position: relative;
}

.store-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.store-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.store-name-container {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.store-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 10rpx;
}

.main-tag {
  background-color: #ff9500;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.store-address {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.store-meta {
  display: flex;
  align-items: center;
}

.store-id {
  font-size: 22rpx;
  color: #999;
}

/* 租户数据 */
.store-card-body {
  padding: 20rpx 30rpx;
}

.store-data {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.data-item {
  display: flex;
  flex-direction: column;
}

.data-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.data-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.data-tag {
  display: inline-block;
  font-size: 24rpx;
  /* padding: 4rpx 12rpx; */
  border-radius: 4rpx;
  font-weight: normal;
}

.status-pending {
  color: #909399;
}

.status-processing {
  color: #ff9500;
}

.status-passed {
  color: #34c759;
}

.status-failed {
  color: #ff3b30;
}

.store-safety {
  padding-top: 20rpx;
}

.safety-label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.safety-status {
  font-size: 28rpx;
  font-weight: 600;
}

.safety-pending {
  color: #909399;
}

.safety-processing {
  color: #ff9500;
}

.safety-passed {
  color: #34c759;
}

.safety-failed {
  color: #ff3b30;
}

/* 操作按钮 */
.store-card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx 30rpx;
  gap: 16rpx;
}

.action-btn {
  height: 60rpx;
  line-height: 60rpx;
  font-size: 26rpx;
  border-radius: 6rpx;
  background-color: var(--primary-color);
  color: #fff;
  text-align: center;
  width: 80%;
  border-radius: 8rpx;
}

.action-btn.outline {
  background-color: #fff;
  border: 1rpx solid #ddd;
  color: #666;
}

.action-btn::after {
  border: none;
}
</style> 