<template>
  <view class="container">
    <view class="content">
      <view class="store-info card">
        <view class="card-title">租户信息</view>
        <view class="info-item">
          <text class="label">租户名称</text>
          <text class="value">{{storeInfo.name}}</text>
        </view>
        <view class="info-item">
          <text class="label">租户地址</text>
          <text class="value">{{storeInfo.address}}</text>
        </view>
      </view>
      
      <view class="form card">
        <view class="card-title">预约信息</view>
        
        <view class="form-item" :class="{'form-error': errors.contactName}">
          <text class="label required">联系人</text>
          <input 
            type="text" 
            v-model="form.contactName" 
            placeholder="请输入联系人姓名" 
            class="input" 
            @input="validateField('contactName')"
            @blur="validateField('contactName')"
          />
          <text v-if="errors.contactName" class="error-message">{{errors.contactName}}</text>
        </view>
        
        <view class="form-item" :class="{'form-error': errors.contactPhone}">
          <text class="label required">联系电话</text>
          <input 
            type="number" 
            v-model="form.contactPhone" 
            placeholder="请输入联系电话" 
            class="input" 
            @input="validateField('contactPhone')"
            @blur="validateField('contactPhone')"
          />
          <text v-if="errors.contactPhone" class="error-message">{{errors.contactPhone}}</text>
        </view>
        
        <view class="form-item" :class="{'form-error': errors.date}">
          <text class="label required">预约日期</text>
          <picker 
            mode="date" 
            :value="form.date" 
            :start="startDate" 
            :end="endDate" 
            @change="bindDateChange" 
            class="picker"
          >
            <view class="picker-value">{{form.date || '请选择日期'}}</view>
          </picker>
          <text v-if="errors.date" class="error-message">{{errors.date}}</text>
        </view>
        
        <view class="form-item" :class="{'form-error': errors.timeSlot}">
          <text class="label required">预约时段</text>
          <picker 
            mode="selector" 
            :range="timeSlots" 
            :value="timeSlotIndex" 
            @change="bindTimeChange" 
            class="picker"
          >
            <view class="picker-value">{{form.timeSlot || '请选择时段'}}</view>
          </picker>
          <text v-if="errors.timeSlot" class="error-message">{{errors.timeSlot}}</text>
        </view>
        
        <view class="form-item" :class="{'form-error': errors.remarks}">
          <text class="label">备注</text>
          <textarea 
            v-model="form.remarks" 
            placeholder="请输入备注信息（选填）" 
            class="textarea"
            @input="validateField('remarks')"
            @blur="validateField('remarks')"
          />
          <text v-if="errors.remarks" class="error-message">{{errors.remarks}}</text>
          <text class="char-count">{{form.remarks.length}}/200</text>
        </view>
      </view>
      
      <view class="safety-tips card">
        <view class="card-title">安全指导须知</view>
        <view class="tips-content">
          <view class="tip-item">1. 安全指导员将在预约时间上门进行安全检查和指导。</view>
          <view class="tip-item">2. 请确保预约时间有人在场，并准备好相关证件和材料。</view>
          <view class="tip-item">3. 如需取消或变更预约，请提前24小时联系客服。</view>
          <view class="tip-item">4. 安全指导完成后，系统将更新租户的安全指导状态。</view>
        </view>
      </view>
    </view>
    
    <view class="footer">
      <button class="btn-primary submit-btn" @click="submitSchedule" :disabled="isSubmitting">
        {{ isSubmitting ? '提交中...' : '提交预约' }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 响应式数据
const storeId = ref('')
const storeInfo = ref({
  name: '',
  address: ''
})
const form = ref({
  contactName: '',
  contactPhone: '',
  date: '',
  timeSlot: '',
  remarks: ''
})
const timeSlots = ref(['上午 9:00-11:00', '下午 13:00-15:00', '下午 15:00-17:00'])
const timeSlotIndex = ref(0)
const startDate = ref('')
const endDate = ref('')
const errors = ref({
  contactName: '',
  contactPhone: '',
  date: '',
  timeSlot: '',
  remarks: ''
})
const isSubmitting = ref(false)
const formTouched = ref(false)

// 页面加载时的处理
const onLoad = (options) => {
  if (options.id) {
    storeId.value = options.id
    loadStoreInfo()
  }

  // 设置日期范围（当前日期到三个月后）
  const now = new Date()
  startDate.value = formatDate(now)

  const threeMonthsLater = new Date()
  threeMonthsLater.setMonth(now.getMonth() + 3)
  endDate.value = formatDate(threeMonthsLater)
}
// 加载店铺信息
const loadStoreInfo = () => {
  // 实际项目中应该从API获取数据
  // 这里使用模拟数据
  uni.showLoading({
    title: '加载中'
  })

  setTimeout(() => {
    // 模拟API返回数据
    const stores = [
      {
        id: '1',
        name: '城东燃气服务站',
        address: '东城区建国路88号'
      },
      {
        id: '2',
        name: '城西燃气服务中心',
        address: '西城区复兴门外大街15号'
      },
      {
        id: '3',
        name: '南区燃气便民点',
        address: '丰台区丰台路20号'
      },
      {
        id: '4',
        name: '北区燃气综合服务',
        address: '海淀区中关村大街1号'
      }
    ]

    const store = stores.find(item => item.id === storeId.value)
    if (store) {
      storeInfo.value = store
    }

    uni.hideLoading()
  }, 500)
}
// 日期选择器事件
const bindDateChange = (e) => {
  form.value.date = e.detail.value
  validateField('date')
  formTouched.value = true
}

// 时间段选择器事件
const bindTimeChange = (e) => {
  timeSlotIndex.value = e.detail.value
  form.value.timeSlot = timeSlots.value[e.detail.value]
  validateField('timeSlot')
  formTouched.value = true
}

// 格式化日期为YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${year}-${month}-${day}`
}
// 验证单个字段
const validateField = (fieldName) => {
  formTouched.value = true

  switch(fieldName) {
    case 'contactName':
      if (!form.value.contactName.trim()) {
        errors.value.contactName = '请输入联系人姓名'
      } else if (form.value.contactName.length < 2) {
        errors.value.contactName = '姓名至少需要2个字符'
      } else if (form.value.contactName.length > 20) {
        errors.value.contactName = '姓名不能超过20个字符'
      } else {
        errors.value.contactName = ''
      }
      break

    case 'contactPhone':
      if (!form.value.contactPhone.trim()) {
        errors.value.contactPhone = '请输入联系电话'
      } else if (!/^1[3-9]\d{9}$/.test(form.value.contactPhone)) {
        errors.value.contactPhone = '请输入正确的手机号码'
      } else {
        errors.value.contactPhone = ''
      }
      break

    case 'date':
      if (!form.value.date) {
        errors.value.date = '请选择预约日期'
      } else {
        // 验证选择的日期是否在允许范围内
        const selectedDate = new Date(form.value.date)
        const now = new Date()
        now.setHours(0, 0, 0, 0) // 设置为当天的开始时间

        if (selectedDate < now) {
          errors.value.date = '预约日期不能早于今天'
        } else {
          errors.value.date = ''
        }
      }
      break

    case 'timeSlot':
      if (!form.value.timeSlot) {
        errors.value.timeSlot = '请选择预约时段'
      } else {
        errors.value.timeSlot = ''
      }
      break

    case 'remarks':
      if (form.value.remarks.length > 200) {
        errors.value.remarks = '备注信息不能超过200个字符'
      } else {
        errors.value.remarks = ''
      }
      break
  }

  return !errors.value[fieldName]
}

// 验证整个表单
const validateForm = () => {
  // 验证所有字段
  const nameValid = validateField('contactName')
  const phoneValid = validateField('contactPhone')
  const dateValid = validateField('date')
  const timeSlotValid = validateField('timeSlot')
  const remarksValid = validateField('remarks')

  return nameValid && phoneValid && dateValid && timeSlotValid && remarksValid
}
// 提交预约
const submitSchedule = () => {
  // 表单验证
  if (!validateForm()) {
    // 显示第一个错误
    for (const key in errors.value) {
      if (errors.value[key]) {
        uni.showToast({
          title: errors.value[key],
          icon: 'none'
        })
        return
      }
    }
    return
  }

  // 防止重复提交
  if (isSubmitting.value) {
    return
  }

  isSubmitting.value = true

  // 显示提交中
  uni.showLoading({
    title: '提交中'
  })

  // 模拟API提交
  setTimeout(() => {
    uni.hideLoading()
    isSubmitting.value = false

    // 显示成功提示
    uni.showToast({
      title: '预约成功',
      icon: 'success'
    })

    // 2秒后返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  }, 1000)
}

// 返回上一页
const goBack = () => {
  // 如果表单已修改，询问用户是否确认离开
  if (formTouched.value) {
    uni.showModal({
      title: '提示',
      content: '表单尚未提交，是否确认离开？',
      success: (res) => {
        if (res.confirm) {
          uni.navigateBack()
        }
      }
    })
  } else {
    uni.navigateBack()
  }
}

// 导出页面生命周期方法供uni-app使用
defineExpose({
  onLoad
})
</script>

<style scoped>
.container {
  padding: 0;
  background-color: #f8f8f8;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding: 30rpx;
  padding-top: 20rpx;
}

.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  border-left: 8rpx solid #4c6ef5;
  padding-left: 20rpx;
}

.store-info {
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  margin-bottom: 20rpx;
}

.label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
}

.value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.input, .picker, .textarea {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  margin-top: 10rpx;
  box-sizing: border-box;
  color: #333;
  background-color: #f9f9f9;
}

.picker {
  display: flex;
  align-items: center;
}

.picker-value {
  color: #333;
}

.textarea {
  height: 160rpx;
  padding: 20rpx;
  line-height: 1.5;
}

.required:after {
  content: "*";
  color: #fa3534;
  margin-left: 6rpx;
}

/* 表单验证相关样式 */
.form-error .input,
.form-error .picker,
.form-error .textarea {
  border-color: #fa3534;
  background-color: rgba(250, 53, 52, 0.05);
}

.error-message {
  font-size: 24rpx;
  color: #fa3534;
  margin-top: 6rpx;
  display: block;
}

.char-count {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  font-size: 24rpx;
  color: #999;
}

.tips-content {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10rpx;
}

.footer {
  padding: 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
}

.submit-btn {
  padding: 0;
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
  background: linear-gradient(135deg, #4c6ef5 0%, #3b5bdb 100%);
  color: #fff;
  border-radius: 45rpx;
  box-shadow: 0 10rpx 20rpx rgba(75, 110, 245, 0.3);
}

.submit-btn[disabled] {
  opacity: 0.6;
}
</style> 