# 工作人员电话拨打功能说明

## 功能概述

在订单详情页面的任务进度中，所有工作人员（配送员和维保员）的名称都可以点击拨打电话，方便用户直接联系相关工作人员。

## 功能特点

### 1. 可视化标识
- 有电话号码的工作人员名称显示为蓝色并带下划线
- 名称后面显示电话图标 📞
- 点击时有轻微的缩放动画效果

### 2. 智能识别
- 自动识别配送员和维保员
- 弹窗标题会显示对应的工作人员类型
- 根据姓名智能判断工作人员类型

### 3. 安全确认
- 点击后弹出确认对话框
- 显示工作人员姓名和电话号码
- 用户确认后才会拨打电话

## 数据结构

每个任务项需要包含以下字段：

```javascript
{
  name: '任务名称',
  worker: '工作人员姓名',
  workerPhone: '工作人员电话号码', // 新增字段
  // ... 其他字段
}
```

## 界面展示

### 配送员任务进度
- 显示"配送员：王师傅 📞"
- 王师傅名称为蓝色可点击链接
- 点击后弹窗："联系配送员"

### 维保员任务进度
- 显示"维保员：李师傅 📞"
- 李师傅名称为蓝色可点击链接
- 点击后弹窗："联系维保员"

## 交互流程

1. **用户点击工作人员姓名**
   - 触发点击事件
   - 显示确认对话框

2. **确认对话框**
   - 标题：联系配送员/维保员
   - 内容：确定要拨打 XXX 的电话 XXXXXXXXXXX 吗？
   - 按钮：拨打 / 取消

3. **用户确认拨打**
   - 调用系统拨号功能
   - 显示"正在拨打电话..."提示
   - 如果失败则显示错误提示

4. **用户取消**
   - 关闭对话框
   - 不执行任何操作

## 样式设计

### 普通工作人员名称
```css
.worker-name {
  color: rgba(255, 255, 255, 0.6);
}
```

### 可点击的工作人员名称
```css
.worker-name.clickable {
  color: #1890ff;
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.2s ease;
}

.worker-name.clickable:active {
  color: #40a9ff;
  transform: scale(0.98);
}
```

### 电话图标
```css
.phone-icon {
  font-size: 16rpx;
  margin-left: 8rpx;
  color: #1890ff;
}
```

## 技术实现

- 使用 `uni.makePhoneCall()` API 调用系统拨号功能
- 通过条件渲染控制电话图标显示
- 使用 CSS 类名控制样式状态
- 通过姓名判断工作人员类型（简单实现）

## 兼容性

- 支持微信小程序
- 支持 H5（需要在支持拨号的环境中）
- 支持 App
- 需要相应平台的拨号权限

## 使用场景

1. **配送过程中**：用户可以直接联系配送员了解配送进度
2. **安装过程中**：用户可以联系维保员沟通安装细节
3. **问题咨询**：遇到问题时快速联系相关工作人员
4. **紧急情况**：需要紧急联系时一键拨打电话
