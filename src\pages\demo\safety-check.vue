<template>
  <view class="container">
    <view class="header">
      <text class="title">安全环境检查功能演示</text>
      <text class="subtitle">维保员现场安全环境检查步骤</text>
    </view>

    <view class="demo-section">
      <view class="section-title">当前状态: {{ currentStatus }}</view>
      
      <!-- 安全环境检查详情 -->
      <view class="safety-check-detail">
        <view class="safety-status" :class="`status-${safetyCheck.status}`">
          <text class="status-icon">{{ safetyCheck.status === 'qualified' ? '✓' : '⚠' }}</text>
          <text class="status-text">
            {{ safetyCheck.status === 'qualified' ? '安全环境合格' : '安全环境不合格' }}
          </text>
        </view>
        
        <!-- 问题描述 -->
        <view v-if="safetyCheck.issues && safetyCheck.issues.length > 0" class="safety-issues">
          <view class="issues-title">发现问题：</view>
          <view v-for="(issue, index) in safetyCheck.issues" :key="index" class="issue-item">
            <text class="issue-text">{{ index + 1 }}. {{ issue }}</text>
          </view>
        </view>
        
        <!-- 现场照片 -->
        <view v-if="safetyCheck.images && safetyCheck.images.length > 0" class="safety-images">
          <view class="images-title">现场照片：</view>
          <view class="images-grid">
            <view
              v-for="(image, index) in safetyCheck.images"
              :key="index"
              class="safety-image-placeholder"
              @click="previewImage(image)"
            >
              <text class="image-text">照片{{ index + 1 }}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="action-buttons">
        <button class="btn btn-primary" @click="toggleStatus">
          切换状态 ({{ safetyCheck.status === 'qualified' ? '改为不合格' : '改为合格' }})
        </button>
        <button class="btn btn-secondary" @click="goBack">
          返回订单详情
        </button>
      </view>
    </view>

    <view class="description">
      <view class="desc-title">功能说明：</view>
      <view class="desc-item">• 维保员到达现场后进行安全环境检查</view>
      <view class="desc-item">• 检查合格：显示绿色状态，可继续作业</view>
      <view class="desc-item">• 检查不合格：显示橙色警告，记录问题和照片</view>
      <view class="desc-item">• 支持问题描述和现场照片记录</view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'

// 响应式数据
const safetyCheck = ref({
  status: 'qualified', // qualified: 合格, unqualified: 不合格
  issues: [],
  images: ['photo1.jpg', 'photo2.jpg']
})

// 计算属性
const currentStatus = computed(() => {
  return safetyCheck.value.status === 'qualified' ? '合格' : '不合格'
})

// 切换状态
const toggleStatus = () => {
  const isCurrentlyQualified = safetyCheck.value.status === 'qualified'
  
  safetyCheck.value.status = isCurrentlyQualified ? 'unqualified' : 'qualified'
  safetyCheck.value.issues = isCurrentlyQualified ? [
    '燃气管道老化，存在漏气风险',
    '通风不良，空气流通不畅',
    '周围有易燃物品堆放'
  ] : []
  
  uni.showToast({
    title: `已切换为${isCurrentlyQualified ? '不合格' : '合格'}状态`,
    icon: 'none'
  })
}

// 预览图片
const previewImage = (image) => {
  uni.showToast({
    title: `预览图片: ${image}`,
    icon: 'none'
  })
}

// 返回
const goBack = () => {
  uni.navigateBack()
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
}

.subtitle {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.demo-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

/* 安全环境检查样式 */
.safety-check-detail {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.safety-status {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
}

.safety-status.status-qualified {
  background: rgba(76, 175, 80, 0.1);
  border: 1rpx solid rgba(76, 175, 80, 0.2);
}

.safety-status.status-unqualified {
  background: rgba(255, 152, 0, 0.1);
  border: 1rpx solid rgba(255, 152, 0, 0.2);
}

.status-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
  font-weight: bold;
}

.safety-status.status-qualified .status-icon {
  color: #4caf50;
}

.safety-status.status-unqualified .status-icon {
  color: #ff9800;
}

.status-text {
  font-size: 28rpx;
  font-weight: 500;
}

.safety-status.status-qualified .status-text {
  color: #4caf50;
}

.safety-status.status-unqualified .status-text {
  color: #ff9800;
}

.safety-issues {
  margin-bottom: 16rpx;
}

.issues-title {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.issue-item {
  margin-bottom: 8rpx;
}

.issue-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.safety-images {
  margin-top: 16rpx;
}

.images-title {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.safety-image-placeholder {
  width: 120rpx;
  height: 120rpx;
  background: #e9ecef;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.safety-image-placeholder:active {
  transform: scale(0.95);
}

.image-text {
  font-size: 20rpx;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-secondary {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.description {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10rpx);
}

.desc-title {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.desc-item {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 12rpx;
}
</style>
